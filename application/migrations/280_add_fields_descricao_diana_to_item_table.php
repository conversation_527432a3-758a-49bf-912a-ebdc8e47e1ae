<?php

defined('BASEPATH') || exit('No direct script access allowed');

/**
 * Migration to add 'descricao_supercompleta_diana' field to 'item' table.
 */
class Migration_Add_Fields_Descricao_Diana_To_Item_Table extends CI_Migration
{
    public function up()
    {
        // Adiciona o campo 'descricao_resumida_diana' como TEXT
        if (!$this->db->field_exists('descricao_supercompleta_diana', 'item')) {
            $this->dbforge->add_column('item', array(
                'descricao_supercompleta_diana' => array(
                    'type' => 'TEXT',
                    'null' => true
                )
            ));
        }
    }

    public function down()
    {
        // Remove o campo 'descricao_supercompleta_diana'
        if ($this->db->field_exists('descricao_supercompleta_diana', 'item')) {
            $this->dbforge->drop_column('item', 'descricao_supercompleta_diana');
        }
    }
}
