<?php

/**
 * Serviço para integração com a API DIANA - Responder Perguntas
 *
 * Responsável por encapsular toda a lógica de comunicação com a API externa
 * da DIANA para análise de documentos e geração de sugestões de respostas.
 * Inclui validação de payload, tratamento de erros, logging e formatação
 * da resposta para o formato esperado pelo frontend.
 * @property CI_Curl $curl
 * @property Ctr_diana_logs_model $ctr_diana_logs_model
 * @property CI_Config $config
 * @property CI_DB_query_builder $db
 * @property CI_Loader $load
 * @property CI_Input $input
 * @property CI_Controller $CI
 * @see application/models/ctr_diana_logs_model.php
 * @see application/migrations/279_create_table_ctr_diana_logs.php
 * @see Ctr_diana_logs_model
 * @property Ctr_diana_logs_model $ctr_diana_logs_model
 * @property Item_model $item_model
 */
class Diana_service
{
    /**
     * The CodeIgniter super-object.
     *
     * @var CI_Controller & object{load: CI_Loader, ctr_diana_logs_model: Ctr_diana_logs_model, config: CI_Config, db: CI_DB_query_builder, input: CI_Input, curl: CI_Curl}
     */
    protected $CI;
    private $diana_enabled;
    private $diana_base_url;
    private $diana_timeout;
    private $max_file_mb;
    private $max_total_mb;

    public function __construct()
    {
        $this->CI = &get_instance();
        // $this->CI->load->library('curl');
        $this->CI->load->model('ctr_diana_logs_model');

        // Carregar configurações
        $this->diana_enabled = $this->CI->config->item('diana_enabled') ?? false;
        $this->diana_base_url = $this->CI->config->item('diana_base_url') ?? '';
        $this->diana_timeout = $this->CI->config->item('diana_timeout') ?? 30;
        $this->max_file_mb = $this->CI->config->item('diana_max_file_mb') ?? 10;
        $this->max_total_mb = $this->CI->config->item('diana_max_total_mb') ?? 30;
    }

    /**
     * Analisa documentos e perguntas usando a API DIANA
     *
     * @param array $payload Dados para envio à DIANA
     * @param int $id_empresa ID da empresa
     * @param int $id_usuario ID do usuário
     * @return array Resposta padronizada
     */
    public function analisar($payload, $id_empresa, $id_usuario)
    {
        // Validar se o serviço está habilitado
        if (!$this->diana_enabled) {
            return $this->build_error_response('DISABLED', 'Serviço DIANA desabilitado');
        }

        // Validar payload
        $validation_result = $this->validate_payload($payload);
        if (!$validation_result['valid']) {
            return $this->build_error_response('INVALID_PAYLOAD', $validation_result['message']);
        }

        // Preparar dados para log
        $log_data = [
            'id_empresa' => $id_empresa,
            'id_usuario' => $id_usuario,
            'created_at' => date('Y-m-d H:i:s'),
            'request_json' => $this->prepare_request_for_log($payload)
        ];

        try {
            // Fazer chamada para a API DIANA
            $response = $this->call_diana_api($payload);

            // Processar resposta
            $processed_response = $this->process_diana_response($response);

            // Registrar sucesso no log
            $log_data['status_code'] = 200;
            $log_data['ok'] = 1;
            $log_data['response_json'] = json_encode($processed_response);

            $this->save_log($log_data);

            return $processed_response;
        } catch (Exception $e) {
            // Registrar erro no log
            $log_data['status_code'] = $e->getCode() ?: 500;
            $log_data['ok'] = 0;
            $log_data['erro'] = $this->classify_error($e);
            $log_data['response_json'] = json_encode(['error' => $e->getMessage()]);

            $this->save_log($log_data);

            return $this->build_error_response($log_data['erro'], $e->getMessage());
        }
    }

    /**
     * Valida o payload de entrada
     *
     * @param array $payload
     * @return array
     */
    private function validate_payload($payload)
    {
        // Verificar se tem documentos
        if (empty($payload['documentos_upload']) || !is_array($payload['documentos_upload'])) {
            return ['valid' => false, 'message' => 'Nenhum documento fornecido para análise'];
        }

        // Verificar se tem perguntas
        if (empty($payload['perguntas']) || !is_array($payload['perguntas'])) {
            return ['valid' => false, 'message' => 'Nenhuma pergunta fornecida para análise'];
        }

        // Validar documentos
        $total_size = 0;
        foreach ($payload['documentos_upload'] as $doc) {
            if (empty($doc['nome']) || empty($doc['conteudo_base64'])) {
                return ['valid' => false, 'message' => 'Documento com dados incompletos'];
            }

            // Calcular tamanho do arquivo (base64 é ~33% maior que o original)
            $file_size_mb = (strlen($doc['conteudo_base64']) * 0.75) / (1024 * 1024);

            if ($file_size_mb > $this->max_file_mb) {
                return ['valid' => false, 'message' => "Arquivo {$doc['nome']} excede o limite de {$this->max_file_mb}MB"];
            }

            $total_size += $file_size_mb;
        }

        if ($total_size > $this->max_total_mb) {
            return ['valid' => false, 'message' => "Tamanho total dos arquivos excede o limite de {$this->max_total_mb}MB"];
        }

        // Validar perguntas
        foreach ($payload['perguntas'] as $pergunta) {
            if (!isset($pergunta['id']) || empty($pergunta['pergunta'])) {
                return ['valid' => false, 'message' => 'Pergunta com dados incompletos (ID e pergunta são obrigatórios)'];
            }
        }

        return ['valid' => true, 'message' => 'Payload válido'];
    }

    /**
     * Faz a chamada para a API DIANA
     *
     * @param array $payload
     * @return array
     * @throws Exception
     */
    private function call_diana_api($payload)
    {
        $url = rtrim($this->diana_base_url, '/') . '/perguntas';

        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => $this->diana_timeout,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);

        curl_close($ch);

        if ($curl_error) {
            throw new Exception("Erro de conexão com DIANA: {$curl_error}", 0);
        }

        if ($http_code === 0) {
            throw new Exception("Timeout na conexão com DIANA", 408);
        }

        if ($http_code >= 400) {
            $error_msg = "Erro HTTP {$http_code} da API DIANA";
            if ($response) {
                $decoded = json_decode($response, true);
                if (isset($decoded['message'])) {
                    $error_msg .= ": {$decoded['message']}";
                }
            }
            throw new Exception($error_msg, $http_code);
        }

        $decoded_response = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception("Resposta inválida da API DIANA: " . json_last_error_msg(), 500);
        }

        return $decoded_response;
    }

    /**
     * Processa a resposta da API DIANA para o formato padronizado
     *
     * @param array $diana_response
     * @return array
     */
    private function process_diana_response($diana_response)
    {
        $sugestoes = [];

        // Processar baseado na estrutura real retornada pela API
        if (isset($diana_response['respostas']) && is_array($diana_response['respostas'])) {
            foreach ($diana_response['respostas'] as $resposta) {
                $arquivos = [];
                if (!empty($resposta['documento_arquivo'])) {
                    $arquivos[] = [
                        'nome' => $resposta['documento_arquivo'],
                        'page' => $resposta['documento_pagina'] ?? null
                    ];
                }

                $sugestoes[] = [
                    'id' => $resposta['pergunta_id'] ?? null,
                    'pergunta' => '',  // A API não retorna a pergunta, será preenchida no frontend
                    'resposta_sugerida' => !empty($resposta['resposta']) ? $resposta['resposta'] : null,
                    'confianca' => null,  // API não retorna confiança por enquanto
                    'referencias' => [
                        'pagina' => $resposta['documento_pagina'] ?? null,
                        'trecho' => $resposta['documento_trecho'] ?? null,
                        'arquivos' => $arquivos
                    ]
                ];
            }
        }

        return [
            'ok' => true,
            'sugestoes' => $sugestoes
        ];
    }

    /**
     * Prepara dados da requisição para log (remove conteúdo base64 completo)
     *
     * @param array $payload
     * @return string
     */
    private function prepare_request_for_log($payload)
    {
        $log_payload = $payload;

        // Substituir conteúdo base64 por metadados
        if (isset($log_payload['documentos_upload'])) {
            foreach ($log_payload['documentos_upload'] as &$doc) {
                if (isset($doc['conteudo_base64'])) {
                    $size_bytes = strlen($doc['conteudo_base64']) * 0.75; // Aproximação do tamanho real
                    $doc['conteudo_base64'] = "[BASE64_TRUNCATED - {$size_bytes} bytes]";
                }
            }
        }

        return json_encode($log_payload);
    }

    /**
     * Classifica o tipo de erro
     *
     * @param Exception $e
     * @return string
     */
    private function classify_error($e)
    {
        $code = $e->getCode();
        $message = strtolower($e->getMessage());

        if ($code === 408 || strpos($message, 'timeout') !== false) {
            return 'TIMEOUT';
        }

        if ($code >= 400 && $code < 500) {
            return 'DIANA_ERROR';
        }

        if (strpos($message, 'conexão') !== false || strpos($message, 'connection') !== false) {
            return 'TIMEOUT';
        }

        return 'UNKNOWN';
    }

    /**
     * Constrói resposta de erro padronizada
     *
     * @param string $codigo
     * @param string $mensagem
     * @return array
     */
    private function build_error_response($codigo, $mensagem)
    {
        $mensagens_amigaveis = [
            'DISABLED' => 'Funcionalidade temporariamente indisponível',
            'NO_FILES' => 'É necessário enviar pelo menos um arquivo para análise',
            'INVALID_PAYLOAD' => 'Dados inválidos enviados',
            'TIMEOUT' => 'A análise está demorando mais que o esperado. Tente novamente com menos arquivos',
            'DIANA_ERROR' => 'Erro no serviço de análise. Tente novamente em alguns minutos',
            'UNKNOWN' => 'Erro inesperado. Tente novamente.',
            'NO_PERGUNTAS' => 'Nenhuma pergunta fornecida para análise'
        ];

        return [
            'ok' => false,
            'codigo' => $codigo,
            'mensagem' => $mensagens_amigaveis[$codigo] ?? $mensagem
        ];
    }

    /**
     * Salva log no banco de dados
     *
     * @param array $log_data
     */
    private function save_log($log_data)
    {
        try {
            $this->CI->ctr_diana_logs_model->save($log_data);
        } catch (Exception $e) {
            log_message('error', "Erro ao salvar log DIANA: " . $e->getMessage());
        }
    }

    /**
     * Verifica se o serviço está habilitado e configurado
     *
     * @return bool
     */
    public function is_enabled()
    {
        return $this->diana_enabled && !empty($this->diana_base_url);
    }

    /**
     * Obtém descrições e ncms para uma lista de partnumbers
     *
     * @param array $partnumbers
     * @param int $id_empresa
     * @return array
     * @throws Exception
     */
    public function get_descricao_partnumbers($partnumbers, $id_empresa)
    {
        $this->CI->load->model('item_model');
        if (empty($partnumbers) || !is_array($partnumbers)) {
            return [];
        }

        $descricao_partnumbers = '';
        foreach ($partnumbers as $partnumber) {
            $item = $this->CI->item_model->get_descricao_partnumbers($partnumber, $id_empresa);
            
            // Concatenar descrições se houver mais de um partnumber
            if ($item[0]['descricao']) {
                $descricao_partnumbers .= trim($descricao_partnumbers . ' ' . $item[0]['descricao']);
            }
        }

        return $descricao_partnumbers;
    }
}
