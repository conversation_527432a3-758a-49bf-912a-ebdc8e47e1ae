<?php
    $descricao_proposta_completa = isset($descricao_proposta_completa) && $descricao_proposta_completa ? '1' : '0';
    $descricao_mercado_local = isset($descricao_mercado_local) && $descricao_mercado_local ? '1' : '0';
?>
<div id="btn-diana-descricao-proposta-app" style="display: flex; flex-direction: column;">
    <?php if ($descricao_proposta_completa && $descricao_mercado_local) : ?>
        <v-btn-diana-descricao-completa-resumida
            :id_empresa="<?php echo sess_user_company() ?>"
        ></v-btn-diana-descricao-completa-resumida>
    <?php endif; ?>
    <?php if ($descricao_proposta_completa) : ?>
        <v-btn-diana-descricao-completa
            :id_empresa="<?php echo sess_user_company() ?>"
        ></v-btn-diana-descricao-completa>
    <?php endif; ?>
    <?php if ($descricao_mercado_local) : ?>
        <v-btn-diana-descricao-resumida
            :id_empresa="<?php echo sess_user_company() ?>"
        ></v-btn-diana-descricao-resumida>
    <?php endif; ?>
</div>
<script type="text/javascript" src="<?php echo base_url('assets/vuejs/dist/btn-diana-descricao-proposta.js?version='.config_item('assets_version')) ?>"></script>