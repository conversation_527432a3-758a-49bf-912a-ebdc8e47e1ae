#!/usr/bin/env python3
"""
Script para testar a API DIANA - Gestão Tributária
Testa diferentes cenários: conectividade, validação, requisição completa
"""

import requests
import json
import base64
import time
from typing import Dict, Any, List
import argparse
from datetime import datetime, timezone
from pathlib import Path

class DianaAPITester:
    def __init__(self, base_url: str = "http://*************"):
        self.base_url = base_url.rstrip('/')
        self.endpoint = f"{self.base_url}/gt/perguntas"
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

    def test_connectivity(self) -> bool:
        """Testa conectividade básica com o endpoint"""
        print("🔍 Testando conectividade...")
        try:
            response = self.session.options(self.endpoint, timeout=10)
            print(f"✅ Endpoint acessível - Status: {response.status_code}")
            if 'allow' in response.headers:
                print(f"📋 Métodos permitidos: {response.headers['allow']}")
            return True
        except requests.exceptions.RequestException as e:
            print(f"❌ Erro de conectividade: {e}")
            return False

    def _png_1x1_base64(self) -> str:
        """Retorna um PNG 1x1 transparente em base64 (sem quebras)."""
        return (
            "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII="
        )

    def _allowed_ext(self, p: Path) -> bool:
        return p.suffix.lower().lstrip('.') in {"png", "jpg", "jpeg", "pdf"}

    def _file_to_base64(self, p: Path) -> str:
        return base64.b64encode(p.read_bytes()).decode("utf-8")

    def _build_docs_from_files(self, files: List[str]) -> List[Dict[str, str]]:
        docs: List[Dict[str, str]] = []
        for f in files:
            path = Path(f)
            if not path.exists() or not path.is_file():
                raise FileNotFoundError(f"Arquivo não encontrado: {path}")
            if not self._allowed_ext(path):
                raise ValueError(
                    f"Extensão não permitida: {path.suffix}. Permitidas: png, jpg, jpeg, pdf"
                )
            docs.append({
                "nome": path.name,
                "conteudo_base64": self._file_to_base64(path)
            })
        return docs

    def create_test_payload(self, scenario: str = "minimal", files: List[str] | None = None) -> Dict[str, Any]:
        """Cria payloads de teste para diferentes cenários. Se 'files' for informado, monta documentos_upload a partir deles."""
        
    # Cenários base
        payloads = {
            "minimal": {
                "documentos_upload": [],
                "perguntas": [
                    {
                        "id": 1,
                        "pergunta": "Qual é o NCM correto para este produto?"
                    },
                    {
                        "id": 2,
                        "pergunta": "Caso não seja uma parte de uso exclusivo em algum equipamento, favor informar o material constitutivo?"
                    }
                ],
                "contexto": {
                    "cliente_segmento": "Industrial",
                    "produto_descricao": "Produto de teste",
                    "ncm_atual": "12345678",
                    "ncm_fornecedor": "87654321"
                }
            },
            "complete": {
                "documentos_upload": [
                    {
                        "nome": "documento_teste.png",
                        "conteudo_base64": self._png_1x1_base64()
                    }
                ],
                "perguntas": [
                    {
                        "id": 1,
                        "pergunta": "Qual é o NCM correto para este produto?"
                    },
                    {
                        "id": 2,
                        "pergunta": "Caso não seja uma parte de uso exclusivo em algum equipamento, favor informar o material constitutivo?"
                    },
                    {
                        "id": 3,
                        "pergunta": "Quais são as principais características deste produto?"
                    }
                ],
                "contexto": {
                    "cliente_segmento": "Industrial",
                    "produto_descricao": "Equipamento eletrônico para uso industrial",
                    "ncm_atual": "85444290",
                    "ncm_fornecedor": "85444200"
                }
            },
            "only_context": {
                "documentos_upload": [],
                "perguntas": [],
                "contexto": {
                    "cliente_segmento": "Tecnologia",
                    "produto_descricao": "Software empresarial",
                    "ncm_atual": "85234910",
                    "ncm_fornecedor": "85234900"
                }
            },
            "invalid_schema": {
                "perguntas": [
                    {
                        "id": "invalid_id",  # ID deve ser número
                        "pergunta": ""  # Pergunta vazia pode ser inválida
                    }
                ],
                "contexto": {
                    "cliente_segmento": "",  # Campo vazio
                    "ncm_atual": "invalid_ncm"  # NCM inválido
                }
            }
        }
        
        payload = payloads.get(scenario, payloads["minimal"])  # cópia por referência simplificada

        # Se arquivos foram informados, sobrepõe documentos_upload
        if files is not None:
            if len(files) == 0:
                # usa PNG 1x1 embutido como amostra
                payload["documentos_upload"] = [{
                    "nome": "amostra.png",
                    "conteudo_base64": self._png_1x1_base64()
                }]
            else:
                payload["documentos_upload"] = self._build_docs_from_files(files)

        return payload

    def _send_request(self, payload: Dict[str, Any]) -> tuple[requests.Response, float]:
        start_time = time.time()
        response = self.session.post(self.endpoint, json=payload, timeout=30)
        end_time = time.time()
        return response, (end_time - start_time)

    def _parse_and_log_response(self, response: requests.Response, response_time: float, verbose: bool) -> Dict[str, Any]:
        if verbose:
            print(f"⏱️  Tempo de resposta: {response_time:.2f}s")
            print(f"📥 Status HTTP: {response.status_code} - {response.reason}")
        try:
            response_json = response.json()
            if verbose:
                print("📋 Resposta JSON:")
                print(json.dumps(response_json, indent=2, ensure_ascii=False))
            if response.status_code == 200 and verbose:
                respostas = response_json.get('respostas', [])
                print(f"✅ Sucesso! Recebidas {len(respostas)} resposta(s)")
            if response.status_code == 422 and verbose:
                errors = response_json.get('detail', [])
                print(f"⚠️  Erro de validação: {len(errors)} erro(s) encontrado(s)")
        except json.JSONDecodeError:
            response_json = {"raw_text": response.text}
            if verbose:
                print(f"📋 Resposta (texto): {response.text}")
        return {
            "status_code": response.status_code,
            "response_time": response_time,
            "response_data": response_json,
            "success": response.status_code == 200,
            "validation_error": response.status_code == 422,
        }

    def test_api_request(self, scenario: str = "minimal", verbose: bool = True, files: List[str] | None = None) -> Dict[str, Any]:
        """Testa uma requisição específica à API"""
        if verbose:
            print(f"\n🔍 Testando cenário: {scenario.upper()}")
        payload = self.create_test_payload(scenario, files)
        if verbose:
            print(f"📤 Enviando para: {self.endpoint}")
            print("📋 Payload enviado:")
            print(json.dumps(payload, indent=2, ensure_ascii=False))
        try:
            response, response_time = self._send_request(payload)
            parsed = self._parse_and_log_response(response, response_time, verbose)
            return {
                "scenario": scenario,
                "payload_sent": payload,
                **parsed,
            }
        except requests.exceptions.Timeout:
            error_msg = "Timeout - API não respondeu em 30 segundos"
            if verbose:
                print(f"⏰ {error_msg}")
            return {"scenario": scenario, "status_code": None, "success": False, "error": error_msg}
        except requests.exceptions.RequestException as e:
            error_msg = f"Erro na requisição: {e}"
            if verbose:
                print(f"❌ {error_msg}")
            return {"scenario": scenario, "status_code": None, "success": False, "error": error_msg}

    def run_quick_test(self):
        """Executa um teste rápido para verificar se a API está funcionando"""
        print("🚀 Teste Rápido da API DIANA")
        print("=" * 40)
        
        if not self.test_connectivity():
            return False
        
        # Teste apenas o cenário mínimo (com anexo de amostra para garantir 200)
        result = self.test_api_request("minimal", verbose=True, files=[])  # files=[] força anexar PNG 1x1
        
        if result["success"]:
            print(f"\n✅ API FUNCIONANDO! Status: {result['status_code']}")
            return True
        elif result.get("validation_error"):
            print("\n⚠️  API funcionando mas com erro de validação (esperado para teste)")
            return True
        else:
            print(f"\n❌ API com problemas! Status: {result.get('status_code')}")
            return False

    def run_comprehensive_test(self, files: List[str] | None = None, save_path: Path | None = None):
        """Executa todos os testes em sequência"""
        print("🚀 Teste Completo da API DIANA")
        print("=" * 60)
        
        # Teste de conectividade
        if not self.test_connectivity():
            print("❌ Falha na conectividade. Abortando testes.")
            return
        
        # Testes de diferentes cenários
        scenarios = ["minimal", "complete", "only_context", "invalid_schema"]
        results = []
        
        for scenario in scenarios:
            # Anexa amostra no cenário minimal para obter 200 com mais frequência
            if scenario == "minimal":
                use_files = []  # força PNG 1x1 embutido
            else:
                # usa 'files' somente no cenário complete; demais mantêm comportamento original
                use_files = files if scenario in {"complete"} else None
            result = self.test_api_request(scenario, verbose=True, files=use_files)
            results.append(result)
            print("-" * 60)
            time.sleep(1)  # Pausa entre requisições
        
        # Resumo dos resultados
        print("\n" + "=" * 60)
        print("📊 RESUMO DOS TESTES")
        print("=" * 60)
        
        success_count = 0
        for result in results:
            scenario = result["scenario"]
            if result.get("success"):
                status_icon = "✅"
                success_count += 1
            elif result.get("validation_error"):
                status_icon = "⚠️ "
            else:
                status_icon = "❌"
            
            code = result.get("status_code", "N/A")
            time_taken = result.get("response_time", 0)
            
            print(f"{scenario.upper():>15}: {status_icon} Status {code} | {time_taken:.2f}s")
        
        print(f"\nResultado: {success_count}/{len(scenarios)} testes bem-sucedidos")
        
        # Recomendações
        if success_count > 0:
            print("💡 A API está funcionando e pronta para receber requisições!")
        else:
            print("⚠️  A API pode precisar de ajustes antes de estar pronta.")

        # Salvamento opcional dos resultados
        self._maybe_save_results(save_path, results)

    def _maybe_save_results(self, save_path: Path | None, results: List[Dict[str, Any]]):
        if save_path is None:
            return
        try:
            save_path.parent.mkdir(parents=True, exist_ok=True)
            data = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "base_url": self.base_url,
                "endpoint": self.endpoint,
                "results": results,
            }
            save_path.write_text(json.dumps(data, ensure_ascii=False, indent=2))
            print(f"💾 Resultados salvos em: {save_path}")
        except Exception as e:
            print(f"⚠️  Não foi possível salvar os resultados em {save_path}: {e}")


if __name__ == "__main__":
    # Configuração da API (endpoint correto já embutido na classe: http://*************/gt/perguntas)
    API_BASE_URL = "http://*************"

    parser = argparse.ArgumentParser(description="Tester da API DIANA (Gestão Tributária)")
    parser.add_argument("mode", choices=["quick", "full"], help="Tipo de teste a executar")
    parser.add_argument("files", nargs="*", help="Arquivos para anexar (apenas no modo full, cenário 'complete')")
    parser.add_argument("--save", action="store_true", help="Salvar resultados da execução em JSON")
    parser.add_argument("--out", help="Caminho do arquivo JSON de saída (opcional)")
    args = parser.parse_args()

    tester = DianaAPITester(API_BASE_URL)

    # Caminho de saída padrão
    default_results_dir = Path(__file__).parent / "results"
    default_results_dir.mkdir(parents=True, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    default_out = default_results_dir / f"results-{args.mode}-{timestamp}.json"
    out_path = Path(args.out) if args.out else default_out

    if args.mode == "quick":
        ok = tester.run_quick_test()
        if args.save:
            # Salva o último resultado do quick como estrutura simples
            # Rodamos novamente em modo silencioso para capturar o dict
            result = tester.test_api_request("minimal", verbose=False, files=[])
            data = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "base_url": tester.base_url,
                "endpoint": tester.endpoint,
                "results": [result],
            }
            try:
                out_path.parent.mkdir(parents=True, exist_ok=True)
                out_path.write_text(json.dumps(data, ensure_ascii=False, indent=2))
                print(f"💾 Resultado salvo em: {out_path}")
            except Exception as e:
                print(f"⚠️  Não foi possível salvar o resultado em {out_path}: {e}")
        # Retorna código de saída apropriado
        raise SystemExit(0 if ok else 1)
    else:
        save_path = out_path if args.save else None
        tester.run_comprehensive_test(files=args.files, save_path=save_path)