<script setup>
import { computed, onMounted, onUnmounted, ref } from 'vue';
import { randomId, getBase64File } from '../../../helpers'
import DropzoneFile from './DropzoneFile.vue'
import DropzoneLabel from './DropzoneLabel.vue'

const emit = defineEmits(['change'])

const props = defineProps({
    id: {
        type: String,
        required: true
    },
    value: {
        type: [String, Array],
        default: ''
    },
    name: {
        type: String,
        required: true
    },
    label: {
        type: String,
        default: ''
    },
    multiple: {
        type: Boolean,
        default: false
    },
    accept: {
        type: String,
        default: '.'
    },
    error: {
        type: String,
        default: ''
    },
    message: {
        type: String,
        default: 'Selecione arquivos'
    },
    gridCols: {
        type: [Number, String],
        default: 2
    }
})

const DRAG_STATES = {
    DRAG_LEAVE: 0,
    DRAG_ENTER: 1
}

const files = ref([])
const input = ref(null)
const dataTransfer = ref(new DataTransfer())

const dragState = ref(DRAG_STATES.DRAG_LEAVE)

const hasFiles = computed(() => files.value.length)
const populatedFiles = computed(() => files.value.filter(({ isPopulated }) => isPopulated))

const onFileSelect = async (event) => {
    for (const file of Object.values(event.target.files)) {
        addFile(file)
    }

    emit('change', files.value)
}

const addFile = async (file) => {
    await syncNewFile(file)

    syncInputFile()
}

const syncNewFile = async (file) => {
    const newFile = await generateUploadedFile(file)

    if (props.multiple) {
        dataTransfer.value.items.add(file)
        files.value.push(newFile)

        return
    }

    dataTransfer.value.items.clear()
    dataTransfer.value.items.add(file)
    files.value = [newFile]
}

const syncInputFile = () => {
    input.value.files = dataTransfer.value.files
}

const generateUploadedFile = async (file) => ({
    id: randomId(),
    file,
    base64: file.type.includes('image')
        ? await getBase64File(file)
        : null
})

const removeFile = (file) => {
    const index = files.value.findIndex(({ id }) => id === file.id)

    if (!file.isPopulated) {
        dataTransfer.value.items.remove(index - files.length)
        input.value.files = dataTransfer.value.files
    }

    files.value.splice(index, 1)
}

const clearFiles = () => {
    console.log('Clearing files in AppDropzone');
    files.value = []
    dataTransfer.value.items.clear()
    if (input.value) {
        input.value.files = dataTransfer.value.files;
    }
}

const onDrop = (e) => {
    for (const file of Object.values(e.dataTransfer.files)) {
        addFile(file)
    }

    dragState.value = DRAG_STATES.DRAG_LEAVE
}

const onDragEnter = () => {
    dragState.value = DRAG_STATES.DRAG_ENTER
}

const onDragLeave = () => {
    dragState.value = DRAG_STATES.DRAG_LEAVE
}

const generatePopulatedFile = (url) => ({
    id: randomId(),
    url,
    isPopulated: true
})

onMounted(() => {
    if (props.value) {
        files.value = Array.isArray(props.value)
            ? props.value.map((url) => generatePopulatedFile(url))
            : [generatePopulatedFile(props.value)]
    }
    window.addEventListener('clear-dropzone', clearFiles);
})

defineExpose({
    dataTransfer,
    clearFiles
})

onUnmounted(() => {
    window.removeEventListener('clear-dropzone', clearFiles);
});

</script>

<template>
    <div>
        <label
            v-if="label"
            :for="id"
            class="input-label fw-semibold fs-9 align-items-center d-flex pb-2"
        >
            {{ label }}
        </label>

        <label
            @dragover.prevent.stop
            @drop.prevent="onDrop"
            @dragenter.prevent.stop="onDragEnter"
            @dragleave.prevent.stop="onDragLeave"
            :for="id"
            class="app-dropzone"
            :class="{
                'bg-primary bg-opacity-25': dragState === DRAG_STATES.DRAG_ENTER,
                'invalid': error
            }"
            :style="{
                minHeight: '200px',
            }"
        >
            <input
                ref="input"
                :id="id"
                class="d-none"
                type="file"
                :name="name"
                :accept="accept"
                :multiple="multiple"
                @input="onFileSelect"
                @change="syncInputFile"
            />

            <input
                v-for="file in populatedFiles"
                :key="file"
                type="hidden"
                :name="`populated_${name}`"
                :value="file.url"
            />

            <div v-if="!hasFiles" style="pointer-events: none;">
                <DropzoneLabel v-if="dragState === DRAG_STATES.DRAG_LEAVE">
                    {{ message }}
                </DropzoneLabel>
                <DropzoneLabel v-else>
                    Solte para adicionar o arquivo
                </DropzoneLabel>
            </div>
            <div
                v-else
                class="row w-100"
                :class="`row-cols-${gridCols}`"
            >
                <div class="p-2"
                    v-for="file in files"
                    :key="file.id"
                >
                    <DropzoneFile
                        :file="file"
                        @remove-file="removeFile(file)"
                    />
                </div>
            </div>
            <div
                class="mt-2 mx-auto text-gray-600"
                v-if="files.length"
            >
                <span v-if="multiple">
                    Clique ou arraste aqui para adicionar mais
                </span>
                <span v-else>
                    Clique ou arraste aqui para substituir
                </span>
            </div>
            <button
                type="button"
                @click.prevent="clearFiles"
                class="btn bg-white btn-link-danger position-absolute btn-sm d-flex align-items-center gap-2"
                style="bottom: 1rem; right: 1rem;"
                v-if="files.length"
            >
                <span class="bx bx-trash"></span>
                <span>Limpar</span>
            </button>
        </label>
        <div class="invalid-feedback" v-if="error">
            {{ error }}
        </div>
        <div v-if="$slots['hint']" class="fw-light fs-9 text-gray-800 mt-2">
            <slot name="hint"></slot>
        </div>
    </div>
</template>

<style scoped lang="scss">
.app-dropzone {
    position: relative;
    padding: 1rem;
    background-color: var(--bs-gray-200);
    border-radius: var(--bs-border-radius-xl);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: default;
    user-select: none;
    transition-property: all;
    transition-duration: .25s;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='12' ry='12' stroke='%238A9197FF' stroke-width='4' stroke-dasharray='5 12' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
    border-radius: 12px;

    &.invalid {
        background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='12' ry='12' stroke='%23DC3545FF' stroke-width='4' stroke-dasharray='5 12' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
    }

    &:hover {
        background-color: var(--bs-gray-300);
        background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' rx='12' ry='12' stroke='%230D6EFDFF' stroke-width='4' stroke-dasharray='5 12' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
    }
}
</style>
