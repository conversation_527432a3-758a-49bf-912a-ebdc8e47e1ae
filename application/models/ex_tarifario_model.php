<?php

if (!class_exists('Tec_log_alteracao_model')) {
    include APPPATH . 'models/tec_log_alteracao_model' . EXT;
}

class Ex_tarifario_model extends Tec_log_alteracao_model
{
    public $_table = 'tec_ncm_ex_tarif';
    public $_table_impostos = 'vw_cad_item_impostos';

    public function __construct()
    {
        parent::__construct();

        $this->set_tipo_log_alteracao('ex_tarifario');
    }

    public function get_item_impostos_by_id_item($id_item)
    {
        $this->db->where('id_item', $id_item);
        $query = $this->db->get($this->_table_impostos);
        return $query->row();
    }

    public function get_entries_by_ncm_exnumber($ncm, $num_ex)
    {
        $this->db->select('tnet.*, nci.bk, nci.bit');
        $this->db->where('tnet.num_ex', $num_ex);
        $this->db->where('tnet.cod_ncm', $ncm);
        $this->db->join('ncm_impostos nci', 'nci.cod_ncm = tnet.cod_ncm AND nci.num_versao = 0', 'inner');
        $this->db->order_by('tnet.dat_vigencia_ini', 'DESC');
        $limit = null;
        $offset = null;
        $query = $this->db->get($this->_table . ' tnet', $limit, $offset);

        return $query->row();
    }

    public function get_entries_by_ncm($ncm)
    {

        $current_date = date('Y-m-d');

        $query = $this->db->query("
        select t.* from
        (
            select
                ex.*,
                (dat_vigencia_fim >= '{$current_date}') as vigente
            from tec_ncm_ex_tarif ex
            where ex.cod_ncm like '{$ncm}%'
            and cod_tipo_ex < 5
            order by num_ex ASC, dat_vigencia_ini DESC
        ) t group by t.num_ex
        ");

        /*$this->db->select("ex.*, (dat_vigencia_fim >= '{$current_date}') as vigente");
        $this->db->where('cod_tipo_ex <', 5);
        $this->db->like('cod_ncm', $ncm, 'after');

        $this->db->group_by('num_ex');

        $query = $this->db->get($this->_table.' ex');*/

        return $query->result();
    }

    public function get_all_itens_related_ex($ncm, $grupo_tarifario,$ex_ii)
    {
        $id_empresa = sess_user_company();
   
        $not_null = false;
        $or = null;
        $query = null;

        $this->db->select("i.part_number, i.descricao, ci.ncm_proposto, ci.num_ex_ii as num_ex, count(*) as total");
        $this->db->join('item i', 'i.part_number = ci.part_number and i.id_empresa = ci.id_empresa', 'inner');
        $this->db->where("ci.id_empresa = '{$id_empresa}' ", NULL, FALSE); 
        
        foreach ($ex_ii as $k => $e)
        {
            $query .= " {$or} ( ci.ncm_proposto = '{$e->cod_ncm}' AND ci.num_ex_ii = '{$e->num_ex}' )";
            $or = "OR";
        }
        if (!empty($query))
            $query = " AND ( {$query} AND ci.id_empresa = '{$id_empresa}' )";


        $this->db->where("ci.id_empresa = '{$id_empresa}' {$query} ", NULL, FALSE); 
        $this->db->group_by('i.part_number');
        $this->db->order_by('ci.num_ex_ii', 'DESC');
        $query = $this->db->get( 'cad_item ci');
        $result =  $query->result();    

        $not_null = $query->num_rows() > 0 ? true : $not_null;
  
        $result['not_null'] = $not_null;

        return $result;        
    }

    public function get_all_ex_ii_order_by_percent_by_ncm($ncm, $vigente = TRUE, $auto_filtro = FALSE)
    {
        $current_date = date('Y-m-d');
        $id_empresa = sess_user_company();

        $where = '';

        if ($vigente == TRUE) {
            $where = " and dat_vigencia_fim >= '{$current_date}'";
        }

        if ($auto_filtro) {
            $join = ' inner join empresa_ex_tarifario emp on emp.cod_ncm = ex.cod_ncm and emp.cod_tipo_ex = ex.cod_tipo_ex and emp.num_ex = ex.num_ex ';
            $where .= " and emp.id_empresa = '{$id_empresa}'";
        } else {
            $join = NULL;
        }

        $query = $this->db->query("
        select ex.*, DATE_FORMAT(dat_vigencia_ini,'%d/%m/%y') as data_vigencia_ini,
                DATE_FORMAT(dat_vigencia_fim,'%d/%m/%y') as data_vigencia_fim,
        (dat_vigencia_fim >= '{$current_date}') as vigente from tec_ncm_ex_tarif ex
            {$join}
            left join tec_ncm_ex_tarif_ordenacao tec on tec.ncm = ex.cod_ncm and tec.num_ex = ex.num_ex
            where ex.cod_ncm like '{$ncm}%'
            and ex.cod_tipo_ex < 5
            and ex.num_ex IS NOT NULL
            {$where}
            group by ex.num_ex 
            order by tec.percent DESC
        ");

        return $query->result();
    }

    public function get_all_ex_ii_by_ncm($ncm, $vigente = TRUE, $auto_filtro = FALSE)
    {
        $current_date = date('Y-m-d');
        $id_empresa = sess_user_company();

        $where = '';

        if ($vigente == TRUE) {
            $where = " AND ex.dat_vigencia_fim >= '{$current_date}'";
        }

        if ($auto_filtro) {
            $join = ' INNER JOIN empresa_ex_tarifario emp ON emp.cod_ncm = ex.cod_ncm AND emp.cod_tipo_ex = ex.cod_tipo_ex AND emp.num_ex = ex.num_ex ';
            $where .= " AND emp.id_empresa = '{$id_empresa}'";
        } else {
            $join = '';
        }

        $query = $this->db->query("
            SELECT 
                ex.*,
                DATE_FORMAT(ex.dat_vigencia_ini, '%d/%m/%y') AS data_vigencia_ini,
                DATE_FORMAT(ex.dat_vigencia_fim, '%d/%m/%y') AS data_vigencia_fim,
                (ex.dat_vigencia_fim >= '{$current_date}') AS vigente
            FROM
                tec_ncm_ex_tarif ex
            INNER JOIN (
                SELECT 
                    num_ex,
                    MAX(dat_vigencia_fim) AS max_vigencia_fim
                FROM 
                    tec_ncm_ex_tarif
                WHERE
                    cod_ncm LIKE '{$ncm}%'
                    AND cod_tipo_ex < 5
                    AND num_ex IS NOT NULL
                GROUP BY num_ex
            ) max_dates ON ex.num_ex = max_dates.num_ex 
                AND ex.dat_vigencia_fim = max_dates.max_vigencia_fim
            {$join}
            WHERE
                ex.cod_ncm LIKE '{$ncm}%'
                AND ex.cod_tipo_ex < 5
                AND ex.num_ex IS NOT NULL
                {$where}
            ORDER BY ex.num_ex ASC
        ");

        return $query->result();
    }

    // public function get_all_ex_ii_by_ncm($ncm, $vigente = TRUE, $auto_filtro = FALSE)
    // {
    //     $current_date = date('Y-m-d');
    //     $id_empresa = sess_user_company();

    //     $where = '';

    //     if ($vigente == TRUE) {
    //         $where = " and dat_vigencia_fim >= '{$current_date}'";
    //     }

    //     if ($auto_filtro) {
    //         $join = ' inner join empresa_ex_tarifario emp on emp.cod_ncm = ex.cod_ncm and emp.cod_tipo_ex = ex.cod_tipo_ex and emp.num_ex = ex.num_ex ';
    //         $where .= " and emp.id_empresa = '{$id_empresa}'";
    //     } else {
    //         $join = NULL;
    //     }

    //     $query = $this->db->query("
    //     select t.* from
    //     (
    //         select
    //             ex.*,
    //             DATE_FORMAT(dat_vigencia_ini,'%d/%m/%y') as data_vigencia_ini,
    //             DATE_FORMAT(dat_vigencia_fim,'%d/%m/%y') as data_vigencia_fim,
    //             (dat_vigencia_fim >= '{$current_date}') as vigente
    //         from tec_ncm_ex_tarif ex
    //         {$join}
    //         where ex.cod_ncm like '{$ncm}%'
    //         and ex.cod_tipo_ex < 5
    //         and ex.num_ex IS NOT NULL
    //         {$where}
    //         order by ex.num_ex ASC, dat_vigencia_ini DESC
    //     ) t group by t.num_ex
    //     ");

    //     return $query->result();
    // }

    public function get_all_ex_ii_by_ncm_descricao($ncm, $descricao , $vigente = TRUE, $auto_filtro = FALSE)
    {
        $current_date = date('Y-m-d');
        $id_empresa = sess_user_company();

        $where = '';

        if ($vigente == TRUE) {
            $where = " and dat_vigencia_fim >= '{$current_date}'";
        }

        if ($auto_filtro) {
            $join = ' inner join empresa_ex_tarifario emp on emp.cod_ncm = ex.cod_ncm and emp.cod_tipo_ex = ex.cod_tipo_ex and emp.num_ex = ex.num_ex ';
            $where .= " and emp.id_empresa = '{$id_empresa}'";
        } else {
            $join = NULL;
        }

        $query = $this->db->query("
        select t.* from
        (
            select
                ex.*,
                DATE_FORMAT(dat_vigencia_ini,'%d/%m/%y') as data_vigencia_ini,
                DATE_FORMAT(dat_vigencia_fim,'%d/%m/%y') as data_vigencia_fim,
                (dat_vigencia_fim >= '{$current_date}') as vigente
            from tec_ncm_ex_tarif ex
            {$join}
            where ex.cod_ncm like '{$ncm}%'
            and ex.descricao_linha1 like '%{$descricao}%'
            and ex.cod_tipo_ex < 5
            and ex.num_ex IS NOT NULL
            {$where}
            order by ex.num_ex ASC, dat_vigencia_ini DESC
        ) t group by t.num_ex
        ");

        return $query->result();
    }

    public function get_ex_ii_by_ncm($num_ex, $ncm)
    {
        $current_date = date('Y-m-d');

        // $query = $this->db->query("
        // select t.* from
        // (
        //     select
        //         ex.*,
        //         DATE_FORMAT(dat_vigencia_ini,'%d/%m/%Y') as data_vigencia_ini,
        //         DATE_FORMAT(dat_vigencia_fim,'%d/%m/%Y') as data_vigencia_fim,
        //         (dat_vigencia_fim >= '{$current_date}') as vigente
        //     from tec_ncm_ex_tarif ex
        //     where ex.num_ex = '{$num_ex}'
        //     and ex.cod_ncm like '{$ncm}%'
        //     and cod_tipo_ex < 5
        //     and num_ex IS NOT NULL
        //     order by num_ex ASC, dat_vigencia_ini DESC
        // ) t group by t.num_ex limit 1
        // ");

        $query = $this->db->query("
        select t.* from
        (
            select
                ex.*,
                DATE_FORMAT(dat_vigencia_ini,'%d/%m/%Y') as data_vigencia_ini,
                DATE_FORMAT(dat_vigencia_fim,'%d/%m/%Y') as data_vigencia_fim,
                (dat_vigencia_fim >= '{$current_date}') as vigente
            from tec_ncm_ex_tarif ex
            where ex.num_ex = '{$num_ex}'
            and ex.cod_ncm like '{$ncm}%'
            and cod_tipo_ex < 5
            and num_ex IS NOT NULL
            order by num_ex ASC, dat_vigencia_fim DESC
        ) t limit 1
        ");

        return $query->row();
    }

    public function get_ex_ipi_by_ncm($num_ex, $ncm)
    {
        $current_date = date('Y-m-d');

        $query = $this->db->query("
        select t.* from
        (
            select
                ex.*,
                DATE_FORMAT(dat_vigencia_ini,'%d/%m/%Y') as data_vigencia_ini,
                DATE_FORMAT(dat_vigencia_fim,'%d/%m/%Y') as data_vigencia_fim,
                (dat_vigencia_fim >= '{$current_date}') as vigente
            from tec_ncm_ex_tarif ex
            where ex.num_ex = '{$num_ex}'
            and ex.cod_ncm like '{$ncm}%'
            and cod_tipo_ex > 5
            and num_ex IS NOT NULL
            order by num_ex ASC, dat_vigencia_ini DESC
        ) t  limit 1
        ");

        return $query->row();
    }

    public function get_all_ex_ipi_by_ncm($ncm, $vigente = TRUE, $auto_filtro = FALSE)
    {
        $current_date = date('Y-m-d');
        $id_empresa = sess_user_company();

        $where = '';

        if ($vigente == TRUE) {
            $where = " and dat_vigencia_fim >= '{$current_date}'";
        }

        if ($auto_filtro) {
            $join = ' inner join empresa_ex_tarifario emp on emp.cod_ncm = ex.cod_ncm and emp.cod_tipo_ex = ex.cod_tipo_ex and emp.num_ex = ex.num_ex ';
            $where .= " and emp.id_empresa = '{$id_empresa}'";
        } else {
            $join = NULL;
        }

        $query = $this->db->query("
                SELECT ex.*, 
            DATE_FORMAT(ex.dat_vigencia_ini, '%d/%m/%y') AS data_vigencia_ini, 
            DATE_FORMAT(ex.dat_vigencia_fim, '%d/%m/%y') AS data_vigencia_fim, 
            (ex.dat_vigencia_fim >= '{$current_date}') AS vigente
                FROM tec_ncm_ex_tarif ex
                {$join}
                JOIN (
                    SELECT num_ex, MAX(dat_vigencia_ini) AS data_vigencia_ini
                    FROM tec_ncm_ex_tarif
                    WHERE cod_ncm LIKE '{$ncm}%'
                    AND cod_tipo_ex > 5
                    AND num_ex IS NOT NULL
                    AND dat_vigencia_fim >= '{$current_date}'
                    GROUP BY num_ex
                ) max_dates
                ON ex.num_ex = max_dates.num_ex AND ex.dat_vigencia_ini = max_dates.data_vigencia_ini
                WHERE ex.cod_ncm LIKE '{$ncm}%'
                AND ex.cod_tipo_ex > 5
                AND ex.num_ex IS NOT NULL
                {$where}
                ORDER BY ex.num_ex ASC
        ");

        return $query->result();
    }

    public function from_oracle_import_tec_ncm_ex_tarif()
    {
        // import from oracle
        $this->load->helper('text_helper');

        $offset = 0;
        $limit  = 1000;

        $this->alter_ora_sess();

        $query_total = $this->dbora->query('SELECT COUNT(*) as total FROM tec.v_lista_ex');
        $total       = $query_total->row()->TOTAL;

        $total_paginas = ceil($total / $limit);

        $atualizados = $inseridos = 0;

        for ($i = 0; $i < $total_paginas; $i++) {
            $offset = $i * $limit;
            $offset_plus_limit = $offset + $limit;

            $innerquery = "SELECT lx.*, tncm.BK, tncm.BIT FROM tec.v_lista_ex lx LEFT JOIN tec.tec_ncm tncm ON lx.COD_NCM = tncm.COD_NCM AND tncm.num_versao = 0";

            $query = $this->dbora->query(
                "SELECT * FROM (select inner_query.*, rownum rnum FROM ($innerquery) inner_query WHERE rownum < {$offset_plus_limit}) WHERE rnum >= {$offset} ORDER BY cod_ncm ASC, pais ASC, medida ASC, dat_vigencia_ini ASC"
            );
            
            foreach ($query->result() as $tec) {
                $data = array();

                $dat_vigencia_ini_oracle = trim($tec->DAT_VIGENCIA_INI);
                $dat_vigencia_ini_busca = date('Y-m-d', strtotime($dat_vigencia_ini_oracle));

                $dat_vigencia_fim_oracle = trim($tec->DAT_VIGENCIA_FIM);
                $dat_vigencia_fim_busca = date('Y-m-d', strtotime($dat_vigencia_fim_oracle));

                $data['cod_ncm']           = $cod_ncm = trim($tec->COD_NCM);
                $data['cod_tipo_ex']       = $cod_tipo_ex = trim($tec->COD_TIPO_EX);
                $data['pais']              = $pais = trim($tec->PAIS);
                $data['medida']            = $medida = trim($tec->MEDIDA);
                $data['qtd_quota']         = $qtd_quota = $tec->QTD_QUOTA;
                $data['num_ex']            = $num_ex = $tec->NUM_EX;
                $data['dat_vigencia_ini']  = $dat_vigencia_ini = $dat_vigencia_ini_busca;
                $data['dat_vigencia_fim']  = $dat_vigencia_fim = $dat_vigencia_fim_busca;
                $data['pct_ii']            = $pct_ii = $tec->PCT_II;
                $data['pct_ipi']           = $pct_ipi = $tec->PCT_IPI;
                $data['descricao_linha1']  = $descricao_linha1 = $tec->DESCRICAO_LINHA1;
                $data['descricao_linha2']  = $descricao_linha2 = $tec->DESCRICAO_LINHA2;
                $data['direito_aplicado1'] = $direito_aplicado1 = $tec->DIREITO_APLICADO1;
                $data['direito_aplicado2'] = $direito_aplicado2 = $tec->DIREITO_APLICADO2;
                $data['direito_aplicado3'] = $direito_aplicado3 = $tec->DIREITO_APLICADO3;
                $data['anotacoes']         = $anotacoes = $tec->ANOTACOES;
                $data['dat_inclusao_reg']  = $dat_inclusao_reg = $tec->DAT_INCLUSAO_REG;
                $data['ind_automotivo']    = $ind_automotivo = $tec->IND_AUTOMOTIVO;
                $data['camex']             = $camex = $tec->CAMEX;
                $data['num_anexo']         = $num_anexo = $tec->NUM_ANEXO;

                $chave = array();

                switch ($cod_tipo_ex) {
                    case TEC_NCM_DEFESA_COMERCIAL:
                        $chave = array(
                            'cod_ncm' => $cod_ncm,
                            'pais' => $pais,
                            'medida' => $medida,
                            'dat_vigencia_ini' => $dat_vigencia_ini,
                            'cod_tipo_ex' => $cod_tipo_ex,
                            'anotacoes' => $anotacoes
                        );
                        break;

                    case TEC_NCM_EXCECAO_BIT:
                        $chave = array(
                            'cod_ncm' => $cod_ncm,
                            'dat_vigencia_ini' => $dat_vigencia_ini,
                            'cod_tipo_ex' => $cod_tipo_ex,
                        );
                        break;

                    case TEC_NCM_QUOTAS:
                    case TEC_NCM_EXCECAO_TEC:
                    case TEC_NCM_EX_TARIF:
                        $chave = array(
                            'cod_ncm' => $cod_ncm,
                            'num_ex' => $num_ex,
                            'dat_vigencia_ini' => $dat_vigencia_ini,
                            'cod_tipo_ex' => $cod_tipo_ex,
                        );
                        break;

                    case TEC_NCM_EX_IPI:
                        $chave = array(
                            'cod_ncm' => $cod_ncm,
                            'num_ex' => $num_ex,
                            'dat_vigencia_ini' => $dat_vigencia_ini,
                            'cod_tipo_ex' => $cod_tipo_ex
                        );
                        break;
                }

                $this->db->like($chave, NULL, 'none');

                $query_check_imp = $this->db->get('tec_ncm_ex_tarif', 1);

                $check_campos = array();

                switch ($tec->COD_TIPO_EX) {
                    case TEC_NCM_EX_TARIF:
                        $check_campos = array(
                            'dat_vigencia_ini',
                            'dat_vigencia_fim',
                            'pct_ii',
                            'descricao_linha1',
                            'descricao_linha2',
                            'anotacoes'
                        );
                        break;

                    case TEC_NCM_DEFESA_COMERCIAL:
                        $check_campos = array(
                            'dat_vigencia_ini',
                            'dat_vigencia_fim',
                            'descricao_linha1',
                            'descricao_linha2',
                            'direito_aplicado1',
                            'direito_aplicado2',
                            'direito_aplicado3',
                            'anotacoes'
                        );
                        break;

                    case TEC_NCM_EXCECAO_BIT:
                        $check_campos = array(
                            'dat_vigencia_ini',
                            'dat_vigencia_fim',
                            'pct_ii',
                            'descricao_linha1',
                            'descricao_linha2',
                            'anotacoes'
                        );
                        break;

                    case TEC_NCM_EXCECAO_TEC:
                        $check_campos = array(
                            'dat_vigencia_ini',
                            'dat_vigencia_fim',
                            'pct_ii',
                            'descricao_linha1',
                            'descricao_linha2',
                            'anotacoes'
                        );
                        break;

                    case TEC_NCM_QUOTAS:
                        $check_campos = array(
                            'dat_vigencia_ini',
                            'dat_vigencia_fim',
                            'descricao_linha1',
                            'descricao_linha2',
                            'qtd_quota',
                            'pct_ii',
                            'anotacoes'
                        );
                        break;

                    case TEC_NCM_EX_IPI:
                        $check_campos = array(
                            'dat_vigencia_ini',
                            'dat_vigencia_fim',
                            'descricao_linha1',
                            'descricao_linha2',
                            'pct_ipi',
                            'anotacoes'
                        );
                        break;
                }

                $check_campos[] = 'camex';

                if ($query_check_imp->num_rows() > 0) {
                    $modificado_para     = $modificado_de = array();
                    $result_check_imp = $query_check_imp->row();

                    foreach ($data as $key => $value) {
                        if ($value != $result_check_imp->{$key} && in_array($key, $check_campos)) {
                            $modificado_de[$key] = $result_check_imp->{$key};
                            $modificado_para[$key] = $value;
                        }
                    }

                    if (count($modificado_para) > 0) {
                        $objeto = new Tec_objeto_chave_ex_tarifario();
                        $objeto->cod_ncm = $tec->COD_NCM;
                        $objeto->num_ex = $tec->NUM_EX;
                        $objeto->cod_tipo_ex = $tec->COD_TIPO_EX;

                        $this->salvar_log_alteracao($cod_ncm, $objeto, $modificado_de, $modificado_para, 'ALTER');

                        $atualizados++;
                    }

                    $this->db->update(
                        'tec_ncm_ex_tarif',
                        $data,
                        $chave
                    );
                } else {

                    $objeto = new Tec_objeto_chave_ex_tarifario();
                    $objeto->cod_ncm = $tec->COD_NCM;
                    $objeto->num_ex = $tec->NUM_EX;
                    $objeto->cod_tipo_ex = $tec->COD_TIPO_EX;

                    $this->salvar_log_alteracao($cod_ncm, $objeto, array(), $data, 'ADD');

                    $this->db->insert('tec_ncm_ex_tarif', $data);

                    $inseridos++;
                }
            }
        }

        $current_date = date('Y-m-d');

        $this->set_state('filter.date', $current_date);

        // Conta o total de atualizações
        $total_dump = $this->get_date_total_log($current_date);

        if ($total_dump > 0) {
            $data['base_url'] = config_item('online_url');
            $data['title']    = 'Tabela do EX-Tarifário';
            $data['count']    = $total_dump;
            $data['url_log']  = $data['base_url'] . 'cron/xls_log/' . $this->tipo_alteracao . '/' . $current_date;

            $data['affected_companies'] = $this->get_affected_companies($current_date);

            $html = $this->load->view('templates/notificacao_update', $data, TRUE);

            $this->load->library('email');

            $this->email->from($this->config->item('mail_from_addr'), $this->config->item('mail_from_name'));
            $this->email->to($this->config->item('mail_rcpt_general'));
            $this->email->subject('[Gestão Tarifária] - Tabela do EX-Tarifário');
            $this->email->message($html);

            if ($this->email->send()) {
                $this->email_enviado_log_alteracoes();
            }
        }

        return array('atualizados' => $atualizados, 'inseridos' => $inseridos);
    }

    public function get_empresa_ex_entries($type = 'ii', $limit = null, $offset = null)
    {
        $id_empresa = sess_user_company();
        $current_date = date('Y-m-d');

        if ($search = $this->get_state('filter.search')) {
            $search = trim(addslashes($search));
            $search = str_replace(' ', '%', $search);

            $this->db->where("ex.descricao_linha1 like '%{$search}%'", false, false);
        }

        if ($search_except = $this->get_state('filter.search_except')) {
            $search_except = trim(addslashes($search_except));
            $search_except = str_replace(' ', '%', $search_except);

            $this->db->where("ex.descricao_linha1 not like '%{$search_except}%'", false, false);
        }

        if ($search_ncm = $this->get_state('filter.search_ncm')) {
            $this->db->like('ex.cod_ncm', $search_ncm, 'right');
        }

        $this->db->select("
            ex.*,
            DATE_FORMAT(dat_vigencia_ini,'%d/%m/%Y') as data_vigencia_ini,
            DATE_FORMAT(dat_vigencia_fim,'%d/%m/%Y') as data_vigencia_fim,
            (dat_vigencia_fim >= '{$current_date}') as vigente,
            IF(emp.num_ex IS NOT NULL, 1, 0) as disponivel
        ", false);

        if ($somente_marcados = $this->get_state('filter.somente_marcados')) {
            $emp_method_join = 'inner';
        } else {
            $emp_method_join = 'left';
        }

        $this->db->join(
            "empresa_ex_tarifario emp",
            "emp.num_ex = ex.num_ex and emp.cod_tipo_ex = ex.cod_tipo_ex and emp.cod_ncm = ex.cod_ncm and emp.id_empresa = {$id_empresa}",
            $emp_method_join
        );

        if ($type == 'ii') {
            $this->db->where('ex.cod_tipo_ex <', 5);
        } else {
            $this->db->where('ex.cod_tipo_ex >', 5);
        }

        $this->db->where('ex.num_ex IS NOT NULL', null);
        $this->db->where('ex.dat_vigencia_fim >=', $current_date);

        $this->db->group_by('ex.num_ex, ex.cod_tipo_ex, ex.cod_ncm');

        $this->db->order_by('ex.num_ex ASC');
        $this->db->order_by('ex.dat_vigencia_ini DESC');

        $query = $this->db->get('tec_ncm_ex_tarif ex', $limit, $offset);

        return $query->result();
    }
}
