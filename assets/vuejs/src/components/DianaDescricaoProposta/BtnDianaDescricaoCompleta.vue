<template>
  <button class="btn botao-dropdown-item" @click="handleClick" id="diana_descricao_completa">
    DIANA Descrição proposta completa
  </button>
</template>
<script>
export default {
  data() {
    return {
      url: $base_url + "atribuir_grupo/xhr_sugerir_descricao_diana_completa",
    }
  },
  props: {
    id_empresa: {
      required: true,
      type: String | Number
    }
  },
  methods: {
      async handleClick() {
        const selectedItems = this.buscarItensSelecionados();
        if (selectedItems.length === 0) {
          swal('Atenção!', 'Nenhum item foi selecionado!', 'warning');
          return;
        }
        if (selectedItems.length != 1) {
          swal('Atenção!', 'Apenas um item deve ser selecionado!', 'warning');
          return;
        }

        if ($('#descricao_proposta_completa').val() != null && $('#descricao_proposta_completa').val() != '') {
          return;
        }

        this.buscarSugestaoDescricaoCompletaDiana(selectedItems);
      },
      buscarItensSelecionados() {
        const id_empresa = this.id_empresa;
        return $(".item_selected:checked")
          .map(function () {
            return {
              'partnumber': $(this).val(),
              'description': $(this).data('descricao'),
              'establishment': $(this).data('estabelecimento'),
              'company_id': id_empresa,
            }
          })
          .get();
      },
      buscarSugestaoDescricaoCompletaDiana(selectedItems) {
        $('#loading-overlay-diana').show();

        const postData = {
          dataItem: selectedItems,
        };
        $.post(this.url, postData, function (response) {
          if (response && response.status === 200 && response.data && response.data.descricao_completa && response.data.descricao_completa[0] && response.data.descricao_completa[0].detailed_description) {
            $('#descricao_proposta_completa').val(response.data.descricao_completa[0].detailed_description);
            $("#diana_descricao_completa, #diana_descricao_completa_resumida").prop('disabled', true);
          }
          swal('Sucesso!', 'Sugestão de descrição completa Diana aplicada.', 'success');
          $('#loading-overlay-diana').hide();
          return true;
        }).fail(function (error) {
          console.error('Erro ao buscar as sugestões de descrição completa Diana:', error);
          swal('Erro!', 'Erro ao buscar as sugestões de descrição completa Diana.', 'error');
          $('#loading-overlay-diana').hide();
          return true;
        });
      }
  },
}
</script>