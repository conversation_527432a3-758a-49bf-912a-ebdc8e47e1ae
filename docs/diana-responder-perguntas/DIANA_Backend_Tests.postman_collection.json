{"info": {"_postman_id": "diana-backend-tests", "name": "DIANA Backend - Respond<PERSON>", "description": "Collection para testar os endpoints do backend DIANA implementado", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. Status do Serviço", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has service_enabled\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('service_enabled');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/pr/diana/status", "host": ["{{base_url}}"], "path": ["pr", "diana", "status"]}, "description": "Verifica se o serviço DIANA está habilitado e funcionando"}, "response": []}, {"name": "2. <PERSON><PERSON>lise DIANA - Payload Válido", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has ok property\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('ok');", "});", "", "pm.test(\"Response has sugestoes array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('sugestoes');", "    pm.expect(jsonData.sugestoes).to.be.an('array');", "});", "", "pm.test(\"Response time is reasonable\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(30000);", "});", "", "// Log response for debugging", "console.log('Response:', pm.response.json());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"documentos_upload\": [\n    {\n      \"nome\": \"teste.png\",\n      \"conteudo_base64\": \"iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII=\"\n    }\n  ],\n  \"perguntas\": [\n    {\n      \"id\": 1,\n      \"pergunta\": \"Qual é o material constitutivo do item?\"\n    },\n    {\n      \"id\": 2,\n      \"pergunta\": \"Qual a aplicação do item?\"\n    },\n    {\n      \"id\": 3,\n      \"pergunta\": \"Qual a função do item (para que serve)?\"\n    }\n  ],\n  \"contexto\": {\n    \"cliente_segmento\": \"Industrial\",\n    \"produto_descricao\": \"Produto de teste para validação da API DIANA\",\n    \"ncm_atual\": \"73269090\",\n    \"ncm_fornecedor\": \"84212900\"\n  }\n}"}, "url": {"raw": "{{base_url}}/pr/diana/analisar", "host": ["{{base_url}}"], "path": ["pr", "diana", "analisar"]}, "description": "Testa análise com payload válido contendo documento e perguntas típicas"}, "response": []}, {"name": "3. <PERSON><PERSON><PERSON>e DIANA - <PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response indicates error\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.ok).to.be.false;", "    pm.expect(jsonData).to.have.property('codigo');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"documentos_upload\": [],\n  \"perguntas\": [\n    {\n      \"id\": 1,\n      \"pergunta\": \"Qual é o material constitutivo do item?\"\n    }\n  ],\n  \"contexto\": {\n    \"cliente_segmento\": \"Industrial\",\n    \"produto_descricao\": \"Produto de teste\",\n    \"ncm_atual\": \"12345678\",\n    \"ncm_fornecedor\": \"87654321\"\n  }\n}"}, "url": {"raw": "{{base_url}}/pr/diana/analisar", "host": ["{{base_url}}"], "path": ["pr", "diana", "analisar"]}, "description": "Testa validação quando não há arquivos enviados"}, "response": []}, {"name": "4. <PERSON><PERSON><PERSON>e DIANA - Payload Inválido", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response indicates error\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.ok).to.be.false;", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"dados_invalidos\": true\n}"}, "url": {"raw": "{{base_url}}/pr/diana/analisar", "host": ["{{base_url}}"], "path": ["pr", "diana", "analisar"]}, "description": "Testa comportamento com payload inválido"}, "response": []}, {"name": "5. Análise DIANA - Arquivo Muito Grande", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 400\", function () {", "    pm.response.to.have.status(400);", "});", "", "pm.test(\"Response indicates file size error\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.ok).to.be.false;", "    pm.expect(jsonData.codigo).to.eql('INVALID_PAYLOAD');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"documentos_upload\": [\n    {\n      \"nome\": \"arquivo_grande.pdf\",\n      \"conteudo_base64\": \"{{large_base64_content}}\"\n    }\n  ],\n  \"perguntas\": [\n    {\n      \"id\": 1,\n      \"pergunta\": \"Teste com arquivo grande\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/pr/diana/analisar", "host": ["{{base_url}}"], "path": ["pr", "diana", "analisar"]}, "description": "Testa validação de tamanho de arquivo (configure large_base64_content nas variáveis)"}, "response": []}, {"name": "6. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has statistics\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('estatisticas');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/pr/diana/estatisticas?data_inicio=2024-01-01&data_fim=2024-12-31", "host": ["{{base_url}}"], "path": ["pr", "diana", "estatisticas"], "query": [{"key": "data_inicio", "value": "2024-01-01"}, {"key": "data_fim", "value": "2024-12-31"}]}, "description": "Obtém estatísticas de uso do serviço DIANA"}, "response": []}, {"name": "7. <PERSON><PERSON><PERSON><PERSON> DIANA - <PERSON>úl<PERSON>los Documentos", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has sugestoes\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.ok).to.be.true;", "    pm.expect(jsonData.sugestoes).to.be.an('array');", "});", "", "console.log('Multiple docs response:', pm.response.json());"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"documentos_upload\": [\n    {\n      \"nome\": \"especificacao.png\",\n      \"conteudo_base64\": \"iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII=\"\n    },\n    {\n      \"nome\": \"foto_produto.png\",\n      \"conteudo_base64\": \"iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYGAAAAAcAAJF2p3BAAAAASUVORK5CYII=\"\n    }\n  ],\n  \"perguntas\": [\n    {\n      \"id\": 1,\n      \"pergunta\": \"Qual é o material constitutivo do item?\"\n    },\n    {\n      \"id\": 2,\n      \"pergunta\": \"Qual a aplicação do item (onde é utilizado)?\"\n    }\n  ],\n  \"contexto\": {\n    \"cliente_segmento\": \"Automotivo\",\n    \"produto_descricao\": \"Componente automotivo com múltiplos documentos\",\n    \"ncm_atual\": \"87089990\",\n    \"ncm_fornecedor\": \"87089999\"\n  }\n}"}, "url": {"raw": "{{base_url}}/pr/diana/analisar", "host": ["{{base_url}}"], "path": ["pr", "diana", "analisar"]}, "description": "Testa análise com múltiplos documentos"}, "response": []}, {"name": "8. <PERSON><PERSON><PERSON><PERSON> Permit<PERSON>", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 405\", function () {", "    pm.response.to.have.status(405);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{base_url}}/pr/diana/analisar", "host": ["{{base_url}}"], "path": ["pr", "diana", "analisar"]}, "description": "Testa que GET não é permitido no endpoint de análise"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Configurações globais", "console.log('Executando testes DIANA Backend...');"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Log global de resultados", "console.log('Response time:', pm.response.responseTime + 'ms');"]}}], "variable": [{"key": "base_url", "value": "http://localhost", "description": "URL base da aplicação"}, {"key": "large_base64_content", "value": "", "description": "Conteúdo base64 grande para teste de limite (configurar manualmente)"}]}