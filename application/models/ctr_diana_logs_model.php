<?php

/**
 * Modelo para tabela de logs da DIANA
 * Inicialmente criada pela migration 279_create_table_ctr_diana_logs.php
 * @see application/migrations/279_create_table_ctr_diana_logs.php
 */
class Ctr_diana_logs_model extends CI_Model
{
    private $table = 'ctr_diana_logs';

    public function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    /**
     * Salva um novo log
     *
     * @param array $data
     * @return int|bool ID do registro inserido ou false em caso de erro
     */
    public function save($data)
    {
        if ($this->db->insert($this->table, $data)) {
            return $this->db->insert_id();
        }

        return false;
    }

    /**
     * Busca logs por empresa
     *
     * @param int $id_empresa
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function get_by_empresa($id_empresa, $limit = 50, $offset = 0)
    {
        $this->db->select('*');
        $this->db->from($this->table);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);

        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Busca logs por usuário
     *
     * @param int $id_usuario
     * @param int $limit
     * @param int $offset
     * @return array
     */
    public function get_by_usuario($id_usuario, $limit = 50, $offset = 0)
    {
        $this->db->select('*');
        $this->db->from($this->table);
        $this->db->where('id_usuario', $id_usuario);
        $this->db->order_by('created_at', 'DESC');
        $this->db->limit($limit, $offset);

        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Busca estatísticas de uso por empresa
     *
     * @param int $id_empresa
     * @param string $data_inicio YYYY-MM-DD
     * @param string $data_fim YYYY-MM-DD
     * @return object
     */
    public function get_estatisticas($id_empresa, $data_inicio = null, $data_fim = null)
    {
        $this->db->select('
            COUNT(*) as total_requests,
            SUM(CASE WHEN ok = 1 THEN 1 ELSE 0 END) as successful_requests,
            SUM(CASE WHEN ok = 0 THEN 1 ELSE 0 END) as failed_requests,
            COUNT(DISTINCT id_usuario) as unique_users
        ');
        $this->db->from($this->table);
        $this->db->where('id_empresa', $id_empresa);

        if ($data_inicio) {
            $this->db->where('created_at >=', $data_inicio . ' 00:00:00');
        }

        if ($data_fim) {
            $this->db->where('created_at <=', $data_fim . ' 23:59:59');
        }

        $query = $this->db->get();
        return $query->row();
    }

    /**
     * Remove logs antigos (para limpeza de dados)
     *
     * @param int $dias_para_manter
     * @return bool
     */
    public function cleanup_old_logs($dias_para_manter = 90)
    {
        $data_limite = date('Y-m-d H:i:s', strtotime("-{$dias_para_manter} days"));

        $this->db->where('created_at <', $data_limite);
        return $this->db->delete($this->table);
    }
}
