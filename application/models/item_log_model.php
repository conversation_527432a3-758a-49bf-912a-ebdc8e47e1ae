<?php

class ItemLog
{
    public $id_item_log = 0;
    public $id_item = 0;
    public $tipo_homologacao = '';
    public $id_usuario = 0;
    public $id_empresa = 0;
    public $titulo = '';
    public $motivo = '';
    public $criado_em = '';
    public $part_number = '';
    public $estabelecimento = '';

    public function __construct($args = array(), $autofill = false)
    {
        if ($autofill && !empty($args)) {
            if (isset($args['titulo']) && !empty($args['titulo'])) {
                $this->titulo = $args['titulo'];
            }

            if (isset($args['criado_em']) && !empty($args['criado_em'])) {
                $this->criado_em = $args['criado_em'];
            } else {
                $this->criado_em = date('Y-m-d H:i:s');
            }

            if (isset($args['id_empresa']) && !empty($args['id_empresa'])) {
                $this->id_empresa = $args['id_empresa'];
            } else {
                $this->id_empresa = sess_user_company();
            }

            if (isset($args['id_usuario']) && !empty($args['id_usuario'])) {
                $this->id_usuario = $args['id_usuario'];
            } else {
                $this->id_usuario = sess_user_id();
            }

            if (isset($args['part_number']) && !empty($args['part_number'])) {
                $this->part_number = $args['part_number'];
            }

            if (isset($args['estabelecimento']) && !empty($args['estabelecimento'])) {
                $this->estabelecimento = $args['estabelecimento'];
            }
        }
    }

    public function toSaveArray()
    {
        return array(
            'id_item' => $this->id_item,
            'tipo_homologacao' => $this->tipo_homologacao,
            'id_usuario' => $this->id_usuario,
            'id_empresa' => $this->id_empresa,
            'titulo' => $this->titulo,
            'motivo' => $this->motivo,
            'criado_em' => $this->criado_em,
            'part_number' => $this->part_number,
            'estabelecimento' => $this->estabelecimento
        );
    }

    public function setMotivo($motivo = '')
    {
        $this->motivo = $motivo;
        return $this;
    }
}

class Item_log_model extends MY_Model
{

    public $_table = 'item_log';

    public function __construct()
    {
        parent::__construct();
    }

    public function save($dbdata, $where = null)
    {
        return $this->db->insert($this->_table, $dbdata);
    }

    public function get_entry($part_number, $id_empresa)
    {
        $this->db->where('part_number', $part_number);
        $this->db->where('id_empresa', $id_empresa);

        $query = $this->db->get($this->_table);

        return $query->row();
    }

    public function get_entries($part_number, $limit = NULL, $offset = NULL)
    {
        $this->db->select('log.*, u.nome as nome_usuario');

        $this->db->where('log.part_number', $part_number);
        $this->db->order_by('criado_em', 'DESC');

        if ($id_empresa = $this->get_state('filter.id_empresa')) {
            $this->db->where('log.id_empresa', $id_empresa);
        }

        if ($estabelecimento = $this->get_state('filter.estabelecimento')) {
            $this->db->where('log.estabelecimento', $estabelecimento);
        }

        $this->db->join('usuario u', 'u.id_usuario = log.id_usuario', 'left');

        $result = $this->db->get($this->_table . ' log', $limit, $offset);

        return $result->result();
    }

    public function get_multiple_log_entries($id_empresa)
    {
        $this->db->where('i.id_empresa', $id_empresa);

        $this->db->select('l.part_number, i.descricao, l.motivo, l.criado_em, l.estabelecimento');

        $this->db->join('item i', 'l.part_number = i.part_number AND l.id_empresa = i.id_empresa AND l.estabelecimento = i.estabelecimento', 'inner');

        if ($search = $this->get_state('filter.search')) {
            $this->db->where("(i.part_number LIKE '%{$search}%' OR i.descricao LIKE '%{$search}%')", NULL, FALSE);
        }

        if ($this->get_state('filter.tag')) {
            $this->db->where('i.tag', $this->get_state('filter.tag'));
        }

        $this->db->join('cad_item c', 'c.part_number=i.part_number AND c.id_empresa=i.id_empresa AND c.estabelecimento = i.estabelecimento', 'left');
        $this->db->where('c.id_item IS NULL', NULL, NULL);

        $query = $this->db->get('item_log l');

        return $query->result();
    }

    public function get_like_status($status,$part_number,$estabelecimento,$id_empresa)
    {
        $this->db->like('motivo', $status);
        $this->db->where('id_empresa', $id_empresa);
        $this->db->where('estabelecimento', $estabelecimento);
        $this->db->where('part_number', $part_number);

        $query = $this->db->get($this->_table);

        return $query->num_rows();
    }

    public function get_total_entries($limit = NULL, $offset = NULL)
    {
        $this->db->select("log.part_number, log.id_empresa, i.descricao as descricao_atual, i.estabelecimento, MAX(log.criado_em) as ultimo_log");

        if ($id_empresa = $this->get_state('filter.id_empresa')) {
            $this->db->where('i.id_empresa', $id_empresa);
        }

        if ($busca_str = $this->get_state('filter.busca')) {
            $busca_str = trim($busca_str);
            $this->db->where("(log.part_number LIKE '%{$busca_str}%' OR i.descricao LIKE '%{$busca_str}%')");
        }

        if ($part_numbers = $this->get_state('filter.part_numbers')) {
            $this->db->where_in('log.part_number', $part_numbers);
        }

        if ($descricao = $this->get_state('filter.descricao')) {
            $this->db->like('i.descricao', $descricao);
        }

        if ($data_ini = $this->get_state('filter.data_ini')) {
            if ($data_fim = $this->get_state('filter.data_fim')) {
                $this->db->having("(MAX(log.criado_em) >= '{$data_ini}' AND MAX(log.criado_em) <= '{$data_fim}')", NULL, FALSE);
            } else {
                $this->db->having("(MAX(log.criado_em) >= '{$data_ini}')", NULL, FALSE);
            }
        } else if ($data_fim = $this->get_state('filter.data_fim')) {
            $this->db->having("(MAX(log.criado_em) <= '{$data_fim}')", NULL, FALSE);
        }

        $this->db->join('item i', 'i.part_number = log.part_number AND i.id_empresa=log.id_empresa AND i.estabelecimento = log.estabelecimento', 'inner');

        $this->db->group_by('part_number, estabelecimento', TRUE);

        $this->db->order_by('ultimo_log', 'DESC');

        $query = $this->db->get($this->_table . ' log');

        return $query->num_rows();
    }

    public function get_all_entries($limit = NULL, $offset = NULL)
    {
        $separator = get_company_separator(sess_user_company());

        $this->db->select("log.part_number, log.id_empresa, i.descricao as descricao_atual, log.estabelecimento, MAX(log.criado_em) as ultimo_log");

        if ($id_empresa = $this->get_state('filter.id_empresa')) {
            $this->db->where('i.id_empresa', $id_empresa);
        }

        $busca_str = $this->get_state('filter.busca');
        $adwhere = "";

        if (strpos($busca_str, '*') !== false) {
            if (is_array($busca_str))
            {
                $busca_str = $busca_str[0];
            }            

            if(substr_count($busca_str, '*') == 1) {

                if (substr($busca_str, 0, 1) === '*' ){
                    $busca_str = str_replace("*", "",  $busca_str);
                    $adwhere  .= ' (i.descricao LIKE "%' . $busca_str. '" OR log.part_number LIKE "%' . $busca_str . '")';
                }else if(substr($busca_str, -1) === '*'){
                    $busca_str = str_replace("*", "",  $busca_str);
                    $adwhere  .= ' (i.descricao LIKE "' . $busca_str. '%" OR log.part_number LIKE "' . $busca_str . '%")';

                }else{
                    $busca_str = str_replace("*", "%",  $busca_str);
                    $adwhere  .= ' (i.descricao LIKE "' . $busca_str. '" OR log.part_number LIKE "' . $busca_str . '")';
                }
            }
             if (substr($busca_str, 0, 1) === '*' && substr($busca_str, -1) === '*' && substr_count($busca_str, '*') <= 2) {
                $busca_str = str_replace("*", "",  $busca_str);
                $adwhere .= ' (i.descricao LIKE "%' . $busca_str. '%" OR log.part_number LIKE "%' . $busca_str . '%")';
            } else {

                if (substr_count($busca_str, '*') >= 2) {
                   
                    $adwhereplus = '';
                

                    $busca_str = str_replace("*", "%", $busca_str);

                    if ($busca_str !== '') {
                        $adwhereplus .= 'i.descricao LIKE "' . $busca_str. '" OR log.part_number LIKE "' . $busca_str . '" OR ';
                    }

                    $adwhereplus = rtrim($adwhereplus, 'OR ');

                    $adwhere .= ' (' . $adwhereplus . ')';
                }
            }

            $this->db->where($adwhere, NULL, FALSE);

        }else{

            // if ($busca_str = $this->get_state('filter.busca')) {
            //     $busca_str = trim($busca_str);
            //     $this->db->where("(log.part_number LIKE '%{$busca_str}%' OR i.descricao LIKE '%{$busca_str}%')");
            // }

            if ($part_numbers = $this->get_state('filter.part_numbers'))
            {

                $pns = !empty($separator) &&
                        count(explode($separator, $part_numbers)) >= 3 ?
                        explode($separator, $part_numbers) :
                        $part_numbers;
 
                if (is_array($pns) && !empty($pns)) {
                    $this->db->where_in('log.part_number', $pns);
                } else {
                    $this->db->where_in('log.part_number', $part_numbers);
                }
            }

            if ($descricao = $this->get_state('filter.descricao')) {
                $this->db->like('i.descricao', $descricao);
            }
        }

        if ($data_ini = $this->get_state('filter.data_ini')) {
            if ($data_fim = $this->get_state('filter.data_fim')) {
                $this->db->having("(MAX(log.criado_em) >= '{$data_ini}' AND MAX(log.criado_em) <= '{$data_fim}')", NULL, FALSE);
            } else {
                $this->db->having("(MAX(log.criado_em) >= '{$data_ini}')", NULL, FALSE);
            }
        } else if ($data_fim = $this->get_state('filter.data_fim')) {
            $this->db->having("(MAX(log.criado_em) <= '{$data_fim}')", NULL, FALSE);
        }

        $this->db->join('item i', 'i.part_number = log.part_number AND i.id_empresa=log.id_empresa AND i.estabelecimento = log.estabelecimento', 'inner');

        $this->db->group_by('log.part_number, log.estabelecimento', TRUE);

        $this->db->order_by('ultimo_log', 'DESC');

        $query = $this->db->get($this->_table . ' log', $limit, $offset);

        return $query->result();
    }

    public function get_ultimo_log_data($part_number, $estabelecimento = NULL)
    {
        $this->db->select_max("criado_em");
        $this->db->where('part_number', $part_number);

        if (!empty($estabelecimento)) {
            $this->db->where('estabelecimento', $estabelecimento);
        }

        $query = $this->db->get($this->_table);

        return $query->row();
    }

    public function get_ultimo_log_sugestao_automatica_diana($part_number, $estabelecimento = NULL)
    {
        $this->db->select("motivo");
        $this->db->where('part_number', $part_number);

        if (!empty($estabelecimento)) {
            $this->db->where('estabelecimento', $estabelecimento);
        }

        $this->db->where('titulo', 'sugestao_automatica_diana');

        $this->db->order_by('criado_em', 'DESC');

        $query = $this->db->get($this->_table);

        return $query->row();
    }

 
}
