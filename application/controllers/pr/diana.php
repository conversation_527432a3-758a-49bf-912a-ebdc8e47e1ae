<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

/**
 * Controller DIANA - Responder <PERSON>
 * 
 * Responsável por gerenciar a análise de documentos pela DIANA
 * para sugestão de respostas às perguntas pendentes.
 * 
 * Compatível com CodeIgniter 2.x
 * 
 * @property MY_Input $input
 * @property CI_Loader $load
 * @property CI_Output $output
 * @property CI_Session $session
 * @property Empresa_model $empresa_model
 * @property Diana_service $diana_service
 */
class Diana extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        $this->load->model(['empresa_model']);
        $this->load->service('Diana_service');
        $this->load->helper(['common_helper']);
    }

    /**
     * Endpoint principal para análise de documentos pela DIANA
     *
     * POST pr/diana/analisar
     *
     * @return void
     */
    public function analisar()
    {
        // Verificar se é uma requisição POST (CodeIgniter 2.x compatível)
        if ($this->input->server('REQUEST_METHOD') !== 'POST') {
            return $this->return_error('Método não permitido', 405);
        }

        // Obter dados da empresa do usuário logado
        $id_empresa = sess_user_company();
        $id_usuario = sess_user_id();

        if (!$id_empresa || !$id_usuario) {
            return $this->return_error('Usuário não autenticado corretamente', 401);
        }

        // Verificar se a empresa tem permissão para usar a funcionalidade
        if (!$this->has_diana_permission($id_empresa)) {
            return $this->return_error('Funcionalidade não habilitada para esta empresa', 403);
        }

        // Verificar se o serviço DIANA está habilitado
        if (!$this->diana_service->is_enabled()) {
            return $this->return_error('Serviço temporariamente indisponível', 503);
        }

        // Obter e validar payload (CodeIgniter 2.x compatível)
        $payload = $this->get_request_payload();
        if (!$payload) {
            return $this->return_error('Dados de requisição inválidos', 400);
        }
 
        // Adicionar dadodos contextuais padrão
        // (podem ser expandidos conforme necessidade futura)
        $payload['contexto'] = [
            'cliente_segmento' => '',
            'produto_descricao' => '',
            'ncm_atual' => '',
            'ncm_fornecedor' => ''
        ];

        // // Pesquisar descricao dos partnumbers ao payload
        if (!empty($payload['partnumbers']) && is_array($payload['partnumbers'])) {
            $partnumbers = $payload['partnumbers'];
            $descricao_partnumbers = $this->diana_service->get_descricao_partnumbers($partnumbers, $id_empresa);
            $payload['contexto']['produto_descricao'] = $descricao_partnumbers;
        }

        // Processar análise via serviço DIANA
        try {
            $result = $this->diana_service->analisar($payload, $id_empresa, $id_usuario);

            if ($result['ok']) {
                $this->return_success($result);
            } else {
                $this->return_error($result['mensagem'], 400, $result['codigo']);
            }
        } catch (Exception $e) {
            log_message('error', 'Erro inesperado na análise DIANA: ' . $e->getMessage());
            $this->return_error('Erro interno do servidor', 500);
        }
    }

    /**
     * Verifica se a empresa tem permissão para usar a funcionalidade DIANA
     *
     * @param int $id_empresa
     * @return bool
     */
    private function has_diana_permission($id_empresa)
    {
        $empresa = $this->empresa_model->get_entry($id_empresa);

        if (!$empresa || !$empresa->funcoes_adicionais) {
            return false;
        }

        $funcoes = explode('|', $empresa->funcoes_adicionais);
        return in_array('responder_perguntas_diana', $funcoes);
    }

    /**
     * Obtém e valida o payload da requisição (CodeIgniter 2.x compatível)
     * 
     * @return array|false
     */
    private function get_request_payload()
    {
        // Ler raw input usando método compatível com CI 2.x
        $raw_input = file_get_contents('php://input');

        if (empty($raw_input)) {
            return false;
        }

        $payload = json_decode($raw_input, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return false;
        }

        // Validações básicas do payload
        if (!isset($payload['documentos_upload']) || !isset($payload['perguntas'])) {
            return false;
        }

        return $payload;
    }

    /**
     * Retorna resposta de sucesso
     * 
     * @param array $data
     */
    private function return_success($data)
    {
        $this->output
            ->set_status_header(200)
            ->set_content_type('application/json')
            ->set_output(json_encode($data));
    }

    /**
     * Retorna resposta de erro
     * 
     * @param string $message
     * @param int $status_code
     * @param string $error_code
     */
    private function return_error($message, $status_code = 400, $error_code = null)
    {
        $response = [
            'ok' => false,
            'mensagem' => $message
        ];

        if ($error_code) {
            $response['codigo'] = $error_code;
        }

        $this->output
            ->set_status_header($status_code)
            ->set_content_type('application/json')
            ->set_output(json_encode($response));
    }

    /**
     * Endpoint para teste de conectividade com a DIANA (debug/desenvolvimento)
     * 
     * GET pr/diana/status
     */
    public function status()
    {
        // Verificar se é GET (CodeIgniter 2.x compatível)
        if ($this->input->server('REQUEST_METHOD') !== 'GET') {
            return $this->return_error('Método não permitido', 405);
        }

        $id_empresa = sess_user_company();

        if (!$this->has_diana_permission($id_empresa)) {
            return $this->return_error('Sem permissão', 403);
        }

        $status = [
            'service_enabled' => $this->diana_service->is_enabled(),
            'has_permission' => true,
            'empresa_id' => $id_empresa,
            'timestamp' => date('Y-m-d H:i:s')
        ];

        $this->return_success($status);
    }

    /**
     * Endpoint para obter estatísticas de uso (opcional - para relatórios futuros)
     * 
     * GET pr/diana/estatisticas
     */
    public function estatisticas()
    {
        // Verificar se é GET (CodeIgniter 2.x compatível)
        if ($this->input->server('REQUEST_METHOD') !== 'GET') {
            return $this->return_error('Método não permitido', 405);
        }

        $id_empresa = sess_user_company();

        if (!$this->has_diana_permission($id_empresa)) {
            return $this->return_error('Sem permissão', 403);
        }

        $this->load->model('ctr_diana_logs_model');

        $data_inicio = $this->input->get('data_inicio');
        $data_fim = $this->input->get('data_fim');

        $stats = $this->ctr_diana_logs_model->get_estatisticas($id_empresa, $data_inicio, $data_fim);

        $this->return_success([
            'estatisticas' => $stats,
            'periodo' => [
                'inicio' => $data_inicio ?: 'ilimitado',
                'fim' => $data_fim ?: 'atual'
            ]
        ]);
    }

    /**
     * Método helper para verificar REQUEST_METHOD
     * 
     * @param string $method
     * @return bool
     */
    private function is_method($method)
    {
        return $this->input->server('REQUEST_METHOD') === strtoupper($method);
    }
}
