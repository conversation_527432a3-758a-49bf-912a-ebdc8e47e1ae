<?php

$assunto = 'Solicitação de Revisão de Informações ERP';
$assunto .= ' - Part-number: ' . $item->part_number;
if(!empty($owner_atual) && $owner_atual[0]->codigo != ""){
    $assunto .= ' - Owner: ' . $owner_atual[0]->codigo . ' - ' . $owner_atual[0]->descricao;
    if($owner_item[0]->nomes_resp != ""){ 
        $assunto .= ' - ' . $owner_item[0]->nomes_resp;
    }
}
$assunto .= ' - Data: ' . date('d/m/Y');
?>

<div class="modal fade" id="modalRevisarInfoERP" tabindex="-1" role="dialog" aria-labelledby="modalRevisarInfoERPLabel" aria-hidden="true">
    <div class="modal-dialog" style="width: 70%;">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="modalRevisarInfoERPLabel">Solicitação de Revisão de Informações ERP</h4>
            </div>
            <div class="modal-body d-flex justify-content-center">
                <form id="formRevisarInfoERP" action="">
                    <div class="form-group col-lg-4 col-sm-4">
                        <label for="unidadeNegocio">Unidade de negócio</label>
                        <input type="text" class="form-control" id="unidadeNegocio" name="unidadeNegocio" value="<?php echo $item->estabelecimento; ?>" disabled>
                    </div>
                    <div class="form-group col-lg-4 col-sm-4">
                        <label for="partNumber">Part-number</label>
                        <input type="text" class="form-control" id="partNumber" name="partNumber" value="<?php echo $item->part_number ?>" disabled>
                    </div>
                    <div class="form-group col-lg-4 col-sm-4">
                        <label for="partNumber">Owner</label>
                        <input type="text" class="form-control" id="owner" data-model="ownerins" name="Owner" disabled value="<?php echo (!empty($owner_atual) ? $owner_atual[0]->codigo : '-') ?> - <?php echo (!empty($owner_atual) ? $owner_atual[0]->descricao : '-') ?> - <?php echo (!empty($owner_item) ? $owner_item[0]->nomes_resp : '-') ?>">
                    </div>
                    <div class="form-group col-lg-12 col-sm-12">
                        <label for="assunto">Assunto</label>
                        <input type="text" class="form-control" id="assunto" name="assunto" disabled value="<?php echo $assunto; ?>">
                    </div>
                    <div class="form-group col-lg-12 col-sm-12">
                        <label for="motivo_revisao_erp">Motivo</label>
                        <textarea class="form-control" id="motivo_revisao_erp" name="motivo_revisao_erp" rows="5"></textarea>
                    </div>
                    <div class="form-group col-lg-12 col-sm-12">
                        <input type="hidden" class="form-control" id="itemDescricao" name="itemDescricao" disabled value="<?php echo $item->descricao; ?>">
                        <input type="hidden" name="estabelecimento" id="estabelecimento" value="<?php echo $item->estabelecimento; ?>">
                    </div>
                </form>
            </div>

            <div class="modal-footer" style="margin: 30px 20px 0 0;">
                <button type="button" class="btn btn-primary" id="preVisualizarEmail"><i class="glyphicon glyphicon-eye-open"></i> Pré-visualizar email</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancelar</button>
                <button type="button" class="btn btn-primary" data-loading-text="Enviando..." id="enviar"><i class="glyphicon glyphicon-send"></i> Enviar</button>
                <i id="spinner" class="fa fa-refresh fa-spin" style="display: none;"></i>
            </div>
        </div>

    </div>
</div>

<script>
    $(document).ready(function() {
        $('#preVisualizarEmail').on('click', function() {
            let unidadeNegocio = $('#unidadeNegocio').val();
            let partNumber = $('#partNumber').val();
            let owner = '<?php echo (!empty($owner_atual) ? $owner_atual[0]->codigo : '-') ?> - <?php echo (!empty($owner_atual) ? $owner_atual[0]->descricao : '-') ?> - <?php echo (!empty($owner_item) ? $owner_item[0]->nomes_resp : '-') ?>';
            let assunto = $('#assunto').val();
            let motivo = $('#motivo_revisao_erp').val();
            let itemDescricao = $('#itemDescricao').val();
            let estabelecimento = $('#estabelecimento').val();
            let status = '<?php echo $item->status_formatado ?>';

            window.open('pre_visualizar_email_infos_erp?unidadeNegocio=' + encodeURIComponent(unidadeNegocio) + '&partNumber=' + encodeURIComponent(partNumber) + '&owner=' + encodeURIComponent(owner) + '&assunto=' + encodeURIComponent(assunto) + '&motivo=' + encodeURIComponent(motivo) + '&itemDescricao=' + encodeURIComponent(itemDescricao) + '&estabelecimento=' + encodeURIComponent(estabelecimento) + '&status=' + encodeURIComponent(status), '_blank');
        });

        function sanitizeString(str) {
            str = str.replace(/[\n\r\t]/g, " ");  // Substitui novas linhas, retorno de carro e tabs por espaços
            str = str.replace(/[\\"']/g, '\\$&'); // Escapa aspas e apóstrofos
            return str;
        }

        $('#enviar').on('click', function() {
            let unidadeNegocio = '<?php echo $item->estabelecimento ?>'
            let partNumber = '<?php echo $item->part_number ?>'
            let owner = '<?php echo (!empty($owner_atual) ? $owner_atual[0]->codigo : '-') ?> - <?php echo (!empty($owner_atual) ? $owner_atual[0]->descricao : '-') ?> - <?php echo (!empty($owner_item) ? $owner_item[0]->nomes_resp : '-') ?>'
            let assunto = '<?php echo $assunto ?>'
            let motivo = $('#motivo_revisao_erp').val();
            let itemDescricao = '<?php echo urlencode($item->descricao) ?>';
            let estabelecimento = '<?php echo $item->estabelecimento ?>';
            let status = '<?php echo $item->status_formatado ?>';
            let urlEnvioEmail = '<?php echo base_url() . 'atribuir_grupo/enviar_email_infos_erp' ?>';

            let item = {part_number: '<?php echo urlencode($item->part_number) ?>', estabelecimento: '<?php echo ($item->estabelecimento) ?>', id_empresa: '<?php echo $item->id_empresa ?>'};

            // console.log(item);

            $('#spinner').show();
            $('#enviar').button('loading');

            $.ajax({
                url: urlEnvioEmail,
                type: 'POST',
                data: {
                    unidadeNegocio: unidadeNegocio,
                    partNumber: partNumber,
                    owner: owner,
                    assunto: assunto,
                    motivo: motivo,
                    itemDescricao: itemDescricao,
                    estabelecimento: estabelecimento,
                    status: status,
                    item: item 
                },

                success: function(data) {

                    $('#spinner').hide();
                    
                    // console.log(data);
                    let resultArray = data.split('|');
                    let result = resultArray[0];
                    let debug = resultArray[1];

                    // console.log(result);

                    if (result === 'success') {
                        $('#modalRevisarInfoERP').modal('hide');
                        $('#detalhes-part-number').modal('hide');
                        swal({
                            title: "Sucesso!",
                            text: "Email(s) enviado(s) com sucesso!",
                            type: "success",
                            showCancelButton: false,
                            confirmButtonColor: "#DD6B55",
                            confirmButtonText: "OK",
                            // closeOnConfirm: true
                        }, function() {
                            location.reload();
                        });
                    } else if(result === 'sem_user') {
                        $('#modalRevisarInfoERP').modal('hide');
                        $('#detalhes-part-number').modal('hide');
                        swal({
                            title: "Info!",
                            text: "Nenhum destinatário encontrado!",
                            type: "info",
                            showCancelButton: false,
                            confirmButtonColor: "#DD6B55",
                            confirmButtonText: "OK",
                            // closeOnConfirm: true
                        }, function() {
                            location.reload();
                        });
                    } else {
                        alert('Erro ao enviar email!');
                    }
                },

                error: function(data) {
                    $('#spinner').hide();
                    alert('Erro ao enviar email!');
                    // console.log(data);
                }
            });
        });

    });
</script>