<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Migration_Alter_Table_Empresa_Add_Column_Diana_Descricao_Proposta_Prompt extends CI_Migration {

    public function up()
    {   
        if ($this->db->table_exists('empresa')) {
            if (!$this->db->field_exists('diana_descricao_proposta_prompt', 'empresa')) {
                $fields = array(
                    'diana_descricao_proposta_prompt' => array(
                        'type' => 'TEXT',
                        'null' => TRUE
                    ),
                );
                $this->dbforge->add_column('empresa', $fields);
            }
        }
        
    }

    public function down()
    {
        if ($this->db->table_exists('empresa')) {
            if ($this->db->field_exists('diana_descricao_proposta_prompt', 'empresa')) {
                $this->dbforge->drop_column('empresa', 'diana_descricao_proposta_prompt');
            }
        }
    }
}
