<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class Respostas extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        $this->load->library('breadcrumbs');

        $this->load->model(array(
            'ctr_pergunta_model',
            'ctr_resposta_model',
            'ctr_anexo_resposta_model',
            'ctr_grupo_model',
            'ctr_pendencias_pergunta_model',
            'item_model',
            'usuario_model'
        ));

        $this->load->helper('common_helper');

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Perguntas', '/pr/perguntas');

        $this->ctr_grupo_model->set_state_store_session(TRUE);
    }

    public function index()
    {
        $data = array();


        $this->render('atribuir_grupo/index', $data);
    }

    public function getPartnumbersSemRespostas()
    {
        try {
            $this->ctr_pendencias_pergunta_model->set_state('filter.status', 1);

            $partnumbersPendentes = $this->ctr_pendencias_pergunta_model->getPartnumbersComPerguntasPendentes();

            return response_json(array(
                'data' => $partnumbersPendentes,
                'msg' => 'Partnumbers com perguntas pendentes',
                'error' => false
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'data' => array(),
                'msg' => 'Erro ao buscar as perguntas',
                'error' => true
            ), 400);
        }
    }

    public function getRespostas()
    {
        $this->load->model('ctr_resposta_model');
        $idEmpresa = sess_user_company();
        $partNumber      = $this->input->get('partnumber');
        $estabelecimento = $this->input->get('estabelecimento');

        $resposta = $this->ctr_resposta_model->get_entry($idEmpresa, $partNumber, $estabelecimento);
        $respostaFormatada = [];
        foreach ($resposta as $r) {
            $respostaFormatada[$r->id_pergunta] = $r->resposta;
        }
        return response_json($respostaFormatada);
    }

    public function responderPerguntas()
    {
        ini_set('memory_limit', -1);
        $post = $this->input->post();

        $infoArquivos = isset($post['infoArquivos']) ? $post['infoArquivos'] : '';

        $postPerguntasBkp = $post['perguntas'];
        $post['perguntas'] = json_decode($post['perguntas']);

        if (!empty($post['perguntas'])) {
            $posts = $post['perguntas'];
            $postsNovo = [];
            foreach ($posts as $pergunta) {
                $pergunta->pergunta = html_entity_decode($pergunta->pergunta);
                $postsNovo[] = $pergunta;
            }
            $post['perguntas'] = $postsNovo;
        } else {
            if (json_last_error() == JSON_ERROR_UTF8) {
                $clean = $this->utf8ize($postPerguntasBkp);
                $post['perguntas'] = json_decode($clean);
            }
        }

        $perguntasRespondidas = array();
        if (!empty($infoArquivos)) {
            $count = count($_FILES['arquivos']['name']);

            $arquivos = array();

            foreach ($infoArquivos as $k => $infoArquivo) {
                $infoArquivo = json_decode($infoArquivo);
                $arquivos[$infoArquivo->ids] = $infoArquivo;

                // unset($infoArquivos[$k]);
            }
        }

        $config = [];
        $config['max_size'] = '62914560';

        try {
            if (isset($post['perguntas']) && empty($post['perguntas'])) {
                return response_json(array(
                    "error" => true,
                    "msg" => 'Não identificamos nenhuma pergunta'
                ));
            }

            $idEmpresa = sess_user_company();
            $idUsuario = sess_user_id();

            $upload_respostas_path = config_item('upload_respostas_path');

            if (!is_dir($upload_respostas_path)) {
                $old = umask(0);
                mkdir($upload_respostas_path, 0777);
                umask($old);
            }

            $config['upload_path'] = $upload_respostas_path;
            $config['allowed_types'] = 'xlsx|jpg|jpeg|png|txt|pdf|docx|tiff|tif';

            $this->load->library('upload');

            if (!is_array($post['perguntas'])) {
                $perguntas = json_decode($post['perguntas']);
            } else {
                $perguntas = $post['perguntas'];
            }

            foreach ($perguntas as $pergunta) {
                $partnumbers = explode(",", $pergunta->part_numbers);

                $ids = explode(",", $pergunta->ids);

                $idRespostas = array();

                foreach ($ids as $id) {
                    if ($pergunta->pendente == '1') {
                        continue;
                    }

                    // if (empty($pergunta->resposta)) {
                    //     continue;
                    // }
                    // $partnumber = trim($partnumber);
                    $id = trim($id);

                    $pergunta_dados_item = $this->ctr_pendencias_pergunta_model->getEntryById($id);

                    $perguntaBd = $this->ctr_pendencias_pergunta_model->atualizarPendencia(array(
                        'pergunta' => $pergunta->pergunta,
                        'pendente' => $pergunta->pendente,
                        // 'part_number' => $partnumber,
                        'estabelecimento' => $pergunta_dados_item->estabelecimento,
                        'id_empresa' => $idEmpresa,
                    ), $id);

                    $part_number     = $pergunta_dados_item->part_number;
                    $estabelecimento = $pergunta_dados_item->estabelecimento;

                    $item = array(
                        'part_number'      => $part_number,
                        'estabelecimento'  => $estabelecimento,
                        'pergunta' => $pergunta->pergunta,
                        'id_empresa' => $idEmpresa,
                        'id_resposta' => $perguntaBd->id
                    );

                    $this->ctr_pendencias_pergunta_model->preparar_notificacao($item);

                    $respostas_item = [];
                    if ($pergunta->pergunta == 'Informar o resumo do item.') {
                        $respostas_item['descricao_proposta_completa'] = $pergunta->resposta;
                    }
                    if ($pergunta->pergunta == 'Informe as observações do item.') {
                        $respostas_item['observacoes'] = $pergunta->resposta;
                    }
                    if (!empty($respostas_item)) {
                        $this->item_model->update_item($part_number, $idEmpresa, $respostas_item, FALSE, $estabelecimento);
                    }

                    $data = [
                        "resposta"        => empty($pergunta->resposta) ? null : $pergunta->resposta,
                        "part_number"     => $part_number,
                        "id_usuario"      => $idUsuario,
                        "id_empresa"      => $idEmpresa,
                        "estabelecimento" => $estabelecimento,
                        "id_pergunta"     => $perguntaBd->id
                    ];

                    $idResposta = $this->ctr_resposta_model->save_answers($data);

                    $texto = "<strong>$perguntaBd->campo_partnumber:</strong> $pergunta->resposta";

                    // atualizando informaçõa no item
                    if (!empty($perguntaBd->inf_partnumber) && !empty($perguntaBd->campo_partnumber)) {
                        $this->item_model->update_item($part_number, $idEmpresa, array(
                            "$perguntaBd->campo_partnumber" => $pergunta->resposta
                        ), $texto, $estabelecimento);
                    }

                    array_push($idRespostas, $idResposta);
                }

                if (!empty($idRespostas)) {
                    $arquivosKey = $pergunta->ids;
                    if (isset($arquivos[$arquivosKey]->nomeArquivos)) {
                        foreach ($arquivos[$arquivosKey]->nomeArquivos as $nomeArquivo) {
                            for ($i = 0; $i < $count; $i++) {
                                if (!empty($_FILES['arquivos']['name'][$i]) && $_FILES['arquivos']['name'][$i] == $nomeArquivo) {
                                    $_FILES['arquivo']['name']     = uniqid() . $_FILES['arquivos']['name'][$i];
                                    $_FILES['arquivo']['type']     = $_FILES['arquivos']['type'][$i];
                                    $_FILES['arquivo']['tmp_name'] = $_FILES['arquivos']['tmp_name'][$i];
                                    $_FILES['arquivo']['error']    = $_FILES['arquivos']['error'][$i];
                                    $_FILES['arquivo']['size']     = $_FILES['arquivos']['size'][$i];

                                    if ($_FILES['arquivo']['size'] > $config['max_size']) {
                                        return response_json(array(
                                            "error" => true,
                                            "msg" => 'O arquivo enviado excede o tamanho máximo permitido de 60 mb'
                                        ));
                                    }

                                    $this->upload->initialize($config);

                                    if (!$this->upload->do_upload('arquivo')) {
                                        throw new Exception($this->upload->display_errors());
                                    } else {
                                        $file_data = $this->upload->data();
                                        $file_ext = strtolower($file_data['file_ext']);

                                        // $full_path = $file_data['full_path'];
                                        // $upload_perguntas_path = config_item('upload_perguntas_path');    

                                        foreach ($idRespostas as $id) {
                                            $this->ctr_anexo_resposta_model->save(array(
                                                "hash"        => "",
                                                "nome"        => $file_data['file_name'],
                                                "criado_em"   => date("Y-m-d H:i:s"),
                                                "id_resposta" => $id
                                            ));
                                        }
                                    }

                                    unset($_FILES['arquivos']['name'][$i]);
                                    unset($_FILES['arquivos']['type'][$i]);
                                    unset($_FILES['arquivos']['tmp_name'][$i]);
                                    unset($_FILES['arquivos']['error'][$i]);
                                    unset($_FILES['arquivos']['size'][$i]);

                                    break;
                                }
                            }
                        }
                    }
                }

                $idRespostas = array();
            }
            $this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
            $this->ctr_pendencias_pergunta_model->unset_state("filter.bulk_item");
            return response_json(array(
                "error" => false,
                "msg" => $perguntasRespondidas
            ));
        } catch (Exception $e) {
            return response_json(array(
                "error" => true,
                "msg" => $e->getMessage()
            ), 400);
        }
    }

    public function getHistoricoItem()
    {
        $this->load->model("ctr_resposta_model");

        try {
            $this->ctr_resposta_model->set_state('filter.partnumber', $this->input->get('partnumber'));
            $this->ctr_resposta_model->set_state('filter.estabelecimento', $this->input->get('estabelecimento'));

            $historicoItem = $this->ctr_resposta_model->getHistoricoItem();

            foreach ($historicoItem as $resposta) {
                $resposta->arquivos = $this->ctr_anexo_resposta_model->getEntriesByResposta($resposta->id);
            }

            return response_json(array(
                'data' => $historicoItem,
                'msg' => 'Partnumbers com perguntas pendentes',
                'error' => false
            ), 200);
        } catch (Exception $e) {
            return response_json(array(
                'data' => array(),
                'msg' => 'Erro ao buscar as perguntas',
                'error' => true
            ), 400);
        }
    }

    private function utf8ize($mixed)
    {
        if (is_array($mixed)) {
            foreach ($mixed as $key => $value) {
                $mixed[$key] = $this->utf8ize($value);
            }
        } else if (is_string($mixed)) {
            return utf8_encode($mixed);
        }
        return $mixed;
    }
}
