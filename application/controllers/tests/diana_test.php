<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

/**
 * Controller de Teste para DIANA - Responder Perguntas
 * 
 * Execute via: http://localhost/tests/diana_test/run_all
 * 
 * @property CI_Loader $load
 * @property CI_Output $output
 * @property CI_Session $session
 * @property Empresa_model $empresa_model
 * @property Diana_service $diana_service
 * @property Ctr_diana_logs_model $ctr_diana_logs_model
 */
class Diana_test extends CI_Controller
{
    private $test_results = [];
    private $test_count = 0;
    private $passed_count = 0;

    public function __construct()
    {
        parent::__construct();

        // Verificar se está em ambiente de desenvolvimento
        if (ENVIRONMENT !== 'development') {
            show_error('Testes só podem ser executados em ambiente de desenvolvimento', 403);
        }

        $this->load->model(['empresa_model', 'ctr_diana_logs_model']);
        $this->load->service('Diana_service');
        $this->load->helper(['url', 'common_helper']);
    }

    /**
     * Executa todos os testes
     */
    public function run_all()
    {
        $this->output_header();

        $this->test_configurations();
        $this->test_database_structure();
        $this->test_service_functionality();
        $this->test_api_connectivity();
        $this->test_logs_functionality();
        $this->test_permission_validation();

        $this->output_summary();
    }

    /**
     * Teste específico de conectividade
     */
    public function test_connectivity()
    {
        $this->output_header();
        $this->test_api_connectivity();
        $this->output_summary();
    }

    /**
     * Teste das configurações
     */
    private function test_configurations()
    {
        $this->add_test_section("Configurações DIANA");

        // Verificar configurações básicas
        $diana_enabled = $this->config->item('diana_enabled');
        $this->assert_true($diana_enabled, "DIANA habilitado");

        $diana_base_url = $this->config->item('diana_base_url');
        $this->assert_not_empty($diana_base_url, "URL base configurada");

        $diana_timeout = $this->config->item('diana_timeout');
        $this->assert_greater_than($diana_timeout, 0, "Timeout configurado");

        // Verificar se o service está funcional
        $this->assert_true($this->diana_service->is_enabled(), "Service DIANA funcional");
    }

    /**
     * Teste da estrutura do banco
     */
    private function test_database_structure()
    {
        $this->add_test_section("Estrutura do Banco de Dados");

        // Verificar se a tabela existe
        $table_exists = $this->db->table_exists('ctr_diana_logs');
        $this->assert_true($table_exists, "Tabela ctr_diana_logs existe");

        if ($table_exists) {
            // Verificar campos obrigatórios
            $required_fields = ['id', 'id_empresa', 'id_usuario', 'created_at', 'status_code', 'ok'];
            foreach ($required_fields as $field) {
                $field_exists = $this->db->field_exists($field, 'ctr_diana_logs');
                $this->assert_true($field_exists, "Campo '{$field}' existe");
            }
        }
    }

    /**
     * Teste da funcionalidade do service
     */
    private function test_service_functionality()
    {
        $this->add_test_section("Funcionalidade do Service");

        // Teste de validação de payload
        $empty_payload = [];
        $validation = $this->call_private_method($this->diana_service, 'validate_payload', [$empty_payload]);
        $this->assert_false($validation['valid'], "Validação rejeita payload vazio");

        // Teste de payload válido
        $valid_payload = [
            'documentos_upload' => [
                [
                    'nome' => 'test.pdf',
                    'conteudo_base64' => base64_encode('test content')
                ]
            ],
            'perguntas' => [
                [
                    'id' => 1,
                    'pergunta' => 'Teste?'
                ]
            ]
        ];

        $validation = $this->call_private_method($this->diana_service, 'validate_payload', [$valid_payload]);
        $this->assert_true($validation['valid'], "Validação aceita payload válido");

        // Teste de arquivo muito grande
        $large_file_payload = [
            'documentos_upload' => [
                [
                    'nome' => 'large.pdf',
                    'conteudo_base64' => str_repeat('A', 15 * 1024 * 1024) // 15MB em base64
                ]
            ],
            'perguntas' => [
                [
                    'id' => 1,
                    'pergunta' => 'Teste?'
                ]
            ]
        ];

        $validation = $this->call_private_method($this->diana_service, 'validate_payload', [$large_file_payload]);
        $this->assert_false($validation['valid'], "Validação rejeita arquivo muito grande");
    }

    /**
     * Teste de conectividade com a API
     */
    private function test_api_connectivity()
    {
        $this->add_test_section("Conectividade API DIANA");

        $diana_base_url = $this->config->item('diana_base_url');
        $url = rtrim($diana_base_url, '/') . '/perguntas';

        // Teste básico de conectividade
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 10,
            CURLOPT_CONNECTTIMEOUT => 5,
            CURLOPT_CUSTOMREQUEST => 'OPTIONS', // Teste sem enviar dados
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false
        ]);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error) {
            $this->assert_false(true, "Erro de conectividade: {$curl_error}");
            return;
        }

        // Aceitar códigos que indicam que o endpoint existe (405, 200, etc.)
        $valid_codes = [200, 405, 404, 500];
        $is_reachable = in_array($http_code, $valid_codes);
        $this->assert_true($is_reachable, "Endpoint acessível (HTTP {$http_code})");

        // Se conseguiu conectar, testar com payload real
        if ($is_reachable && $http_code !== 404) {
            $this->test_real_api_call();
        }
    }

    /**
     * Teste com chamada real à API
     */
    private function test_real_api_call()
    {
        $test_payload = [
            'documentos_upload' => [
                [
                    'nome' => 'test.png',
                    'conteudo_base64' => 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII='
                ]
            ],
            'perguntas' => [
                [
                    'id' => 1,
                    'pergunta' => 'Qual é o material constitutivo?'
                ]
            ],
            'contexto' => [
                'cliente_segmento' => 'Teste',
                'produto_descricao' => 'Produto de teste',
                'ncm_atual' => '12345678',
                'ncm_fornecedor' => '87654321'
            ]
        ];

        try {
            $response = $this->call_private_method($this->diana_service, 'call_diana_api', [$test_payload]);

            if (isset($response['respostas']) && is_array($response['respostas'])) {
                $this->assert_true(true, "API retornou estrutura válida");

                $total_respostas = count($response['respostas']);
                $this->add_info("Total de respostas recebidas: {$total_respostas}");

                // Verificar se pelo menos uma resposta tem dados úteis
                $has_useful_response = false;
                foreach ($response['respostas'] as $resp) {
                    if (!empty($resp['resposta']) || !empty($resp['documento_trecho'])) {
                        $has_useful_response = true;
                        break;
                    }
                }

                if ($has_useful_response) {
                    $this->assert_true(true, "API retornou dados úteis");
                } else {
                    $this->add_info("API conectou mas não retornou dados úteis (esperado para arquivo de teste)");
                }
            } else {
                $this->assert_false(true, "API retornou estrutura inválida");
            }
        } catch (Exception $e) {
            $this->assert_false(true, "Erro na chamada API: " . $e->getMessage());
        }
    }

    /**
     * Teste da funcionalidade de logs
     */
    private function test_logs_functionality()
    {
        $this->add_test_section("Funcionalidade de Logs");

        // Testar gravação de log
        $test_log = [
            'id_empresa' => 1,
            'id_usuario' => 1,
            'created_at' => date('Y-m-d H:i:s'),
            'status_code' => 200,
            'ok' => 1,
            'request_json' => '{"test": true}',
            'response_json' => '{"success": true}'
        ];

        $log_id = $this->ctr_diana_logs_model->save($test_log);
        $this->assert_true($log_id !== false, "Log salvo com sucesso");

        if ($log_id) {
            // Testar busca de logs
            $logs = $this->ctr_diana_logs_model->get_by_empresa(1, 1);
            $this->assert_true(count($logs) > 0, "Logs recuperados por empresa");

            // Limpar log de teste
            $this->db->where('id', $log_id)->delete('ctr_diana_logs');
        }
    }

    /**
     * Teste de validação de permissões
     */
    private function test_permission_validation()
    {
        $this->add_test_section("Validação de Permissões");

        // Tentar encontrar uma empresa de teste
        $empresa = $this->db->limit(1)->get('empresa')->row();

        if ($empresa) {
            $has_permission = false;
            if ($empresa->funcoes_adicionais) {
                $funcoes = explode('|', $empresa->funcoes_adicionais);
                $has_permission = in_array('responder_perguntas_diana', $funcoes);
            }

            $this->add_info("Empresa ID {$empresa->id_empresa} " . ($has_permission ? "TEM" : "NÃO TEM") . " permissão DIANA");

            if (!$has_permission) {
                $this->add_info("Para testar completamente, adicione 'responder_perguntas_diana' nas funções adicionais de uma empresa");
            }
        } else {
            $this->add_info("Nenhuma empresa encontrada para teste de permissões");
        }
    }

    /**
     * Chamada de método privado para testes
     */
    private function call_private_method($object, $method, $args = [])
    {
        $reflection = new ReflectionClass($object);
        $method = $reflection->getMethod($method);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $args);
    }

    /**
     * Adiciona seção de teste
     */
    private function add_test_section($title)
    {
        $this->test_results[] = [
            'type' => 'section',
            'title' => $title
        ];
    }

    /**
     * Adiciona informação
     */
    private function add_info($message)
    {
        $this->test_results[] = [
            'type' => 'info',
            'message' => $message
        ];
    }

    /**
     * Assertion helper
     */
    private function assert_true($condition, $message)
    {
        $this->test_count++;
        $passed = (bool) $condition;

        if ($passed) {
            $this->passed_count++;
        }

        $this->test_results[] = [
            'type' => 'test',
            'passed' => $passed,
            'message' => $message
        ];
    }

    /**
     * Assertion helper
     */
    private function assert_false($condition, $message)
    {
        $this->assert_true(!$condition, $message);
    }

    /**
     * Assertion helper
     */
    private function assert_not_empty($value, $message)
    {
        $this->assert_true(!empty($value), $message);
    }

    /**
     * Assertion helper
     */
    private function assert_greater_than($value, $min, $message)
    {
        $this->assert_true($value > $min, $message);
    }

    /**
     * Saída do cabeçalho
     */
    private function output_header()
    {
        echo "<!DOCTYPE html><html><head><title>Teste DIANA Backend</title>";
        echo "<style>
            body { font-family: monospace; margin: 20px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; }
            .section { margin: 20px 0; padding: 10px; background: #e8f4fd; border-left: 4px solid #2196F3; }
            .test-pass { color: #4CAF50; }
            .test-fail { color: #f44336; }
            .info { color: #666; font-style: italic; }
            .summary { margin: 20px 0; padding: 15px; background: #f0f0f0; border-radius: 3px; }
            h1 { color: #333; text-align: center; }
        </style></head><body>";
        echo "<div class='container'>";
        echo "<h1>🧪 Teste DIANA Backend - Responder Perguntas</h1>";
        echo "<p><strong>Ambiente:</strong> " . ENVIRONMENT . " | <strong>Timestamp:</strong> " . date('Y-m-d H:i:s') . "</p>";
    }

    /**
     * Saída do resumo
     */
    private function output_summary()
    {
        // Processar resultados
        foreach ($this->test_results as $result) {
            switch ($result['type']) {
                case 'section':
                    echo "<div class='section'><strong>{$result['title']}</strong></div>";
                    break;
                case 'test':
                    $class = $result['passed'] ? 'test-pass' : 'test-fail';
                    $icon = $result['passed'] ? '✅' : '❌';
                    echo "<div class='{$class}'>{$icon} {$result['message']}</div>";
                    break;
                case 'info':
                    echo "<div class='info'>ℹ️ {$result['message']}</div>";
                    break;
            }
        }

        // Resumo final
        $success_rate = $this->test_count > 0 ? round(($this->passed_count / $this->test_count) * 100, 1) : 0;
        $status = $success_rate >= 80 ? '🎉 APROVADO' : '⚠️ PRECISA ATENÇÃO';

        echo "<div class='summary'>";
        echo "<h3>{$status}</h3>";
        echo "<p><strong>Total de testes:</strong> {$this->test_count}</p>";
        echo "<p><strong>Aprovados:</strong> {$this->passed_count}</p>";
        echo "<p><strong>Taxa de sucesso:</strong> {$success_rate}%</p>";
        echo "</div>";

        echo "<hr><p><small>Para testar endpoints completos, use: <a href='" . site_url('tests/diana_test/test_endpoints') . "'>Teste de Endpoints</a></small></p>";
        echo "</div></body></html>";
    }

    /**
     * Teste específico de endpoints (requer autenticação)
     */
    public function test_endpoints()
    {
        echo "<h2>Teste de Endpoints</h2>";
        echo "<p>⚠️ Para testar os endpoints completos, você precisa:</p>";
        echo "<ol>";
        echo "<li>Estar logado no sistema</li>";
        echo "<li>Ter uma empresa com 'responder_perguntas_diana' habilitado</li>";
        echo "<li>Usar ferramentas como Postman ou cURL</li>";
        echo "</ol>";

        echo "<h3>Endpoints disponíveis:</h3>";
        echo "<ul>";
        echo "<li><code>POST " . site_url('pr/diana/analisar') . "</code> - Análise principal</li>";
        echo "<li><code>GET " . site_url('pr/diana/status') . "</code> - Status do serviço</li>";
        echo "<li><code>GET " . site_url('pr/diana/estatisticas') . "</code> - Estatísticas</li>";
        echo "</ul>";
    }
}
