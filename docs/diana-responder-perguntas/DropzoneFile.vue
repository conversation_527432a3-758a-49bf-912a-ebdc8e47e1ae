<script setup>
import { computed } from 'vue';
import { last, trimFilename } from '../../../helpers';

const FILE_TYPES = {
    VIDEO: 'VIDEO',
    IMAGE: 'IMAGE',
}

const FILE_EXTENSIONS = {
    VIDEO: ['mp4', 'mkv', 'mpeg4'],
    IMAGE: ['jpg', 'png', 'jpeg', 'svg'],
}

const emit = defineEmits(['remove-file'])
const props = defineProps({
    file: {
        type: Object,
        required: true
    }
})

const fileType = computed(() => {
    const fileExtension = Object.keys(props.file).includes('url')
        ? last(props.file.url.split('.'))
        : last(props.file.file.type.split('/'))

    return Object.entries(FILE_EXTENSIONS)
        .find(
            ([key, extension]) => extension.includes(fileExtension)
        )?.[0] ?? 'FILE'
})

const source = computed(() => {
    if (props.file.base64) {
        return props.file.base64
    }
    return props.file.url
        ? `/storage/${props.file.url}`
        : null
})

const onRemoveClick = () => {
    emit('remove-file')
}
</script>

<template>
    <div class="dropzone-item-card col overflow-hidden"
        @click.stop.prevent
        style="height: 150px;"
    >
        <div class="dropzone-item-action z-1">
            <button
                type="button"
                class="btn btn-link-danger rounded-0"
                @click.stop.prevent="onRemoveClick"
            >
                <span class="bx bx-trash" />
            </button>
        </div>
        <template v-if="fileType === FILE_TYPES.IMAGE">
            <img
                :src="source"
                alt="Pre-visualização da imagem enviada"
                style="height: 100%;"
            />
        </template>
        <div
            v-else-if="fileType === FILE_TYPES.VIDEO"
            class="position-relative h-100"
        >
            <div
                class="d-flex justify-content-center align-items-center position-absolute h-100 w-100"
                :class="{
                    'pb-4': !!file?.file?.name
                }"
            >
                <span class="bx bxs-video text-white bg-dark p-2 bg-opacity-75 rounded-5 fs-5"></span>
            </div>
            <video
                v-if="source"
                class="position-relative h-100 w-100"
                preload="metadata"
            >
                <source :src="source">
            </video>
            <div
                v-else-if="file.file.name"
                class="bottom-0 position-absolute w-100 text-center text-gray-200 mb-4"
            >
                {{ trimFilename(file.file.name) }}
            </div>
        </div>
        <div
            v-else
            class="d-flex flex-column  align-items-center justify-content-center text-gray-200 h-100"
        >
            <span class="fs-1 bx bx-file" />
            <div class="small trim">{{  trimFilename(file?.file?.name || '') }}</div>
        </div>
    </div>
</template>

<style scoped lang="scss">
.dropzone-item-card {
    position: relative;
    background-color: var(--bs-gray-800);
    border-radius: var(--bs-border-radius-lg);
    overflow: auto;
    text-align: center;

    &:hover .dropzone-item-action {
        opacity: 1;
    }

    .dropzone-item-action {
        position: absolute;
        top: 0;
        right: 0;
        background: var(--bs-white);
        border-bottom-left-radius: var(--bs-border-radius-lg);
        opacity: .75;
        transition: .25s opacity;
    }
}
</style>
