# Instruções para Teste do Backend DIANA

## 🎯 Como Testar a Implementação

### 1. Teste via Controller de Teste (Recomendado)

**URL:** `http://localhost/tests/diana_test/run_all`

**O que testa:**

- ✅ Configurações carregadas
- ✅ Estrutura do banco (migração)
- ✅ Funcionalidade do service
- ✅ Conectividade com API DIANA
- ✅ Logs funcionais
- ✅ Validação de permissões

**Execução:**

1. Acesse a URL no navegador
2. Visualize relatório completo em HTML
3. Taxa de sucesso esperada: >80%

**Teste específico de conectividade:**
`http://localhost/tests/diana_test/test_connectivity`

### 2. Preparação do Ambiente

#### a) Executar Migração

```bash
cd /home/<USER>/Face/Becomex/GESTAO-TARIFARIA
php index.php migration migrate
```

#### b) Configurar Empresa de Teste

1. Acesse **Cadastros → Empresa → Editar**
2. Em **Funções Adicionais**, marque: `Responder Perguntas Diana`
3. Salvar

### 3. Teste via Postman (Completo)

#### a) Importar Collection

1. Abrir Postman
2. Import → `docs/diana-responder-perguntas/DIANA_Backend_Tests.postman_collection.json`
3. Configurar variável `base_url` = `http://localhost`

#### b) Executar Testes

**Ordem recomendada:**

1. **Status do Serviço** - Verificar se está ativo
2. **Análise DIANA - Payload Válido** - Teste principal
3. **Sem Arquivos** - Validação de erro
4. **Payload Inválido** - Validação de erro
5. **Estatísticas** - Logs funcionais

#### c) Autenticação Necessária

- Fazer login na aplicação antes
- Postman usará cookies da sessão

### 4. Teste Manual via cURL

#### Verificar Status

```bash
curl -X GET http://localhost/pr/diana/status \
  -H "Accept: application/json" \
  -b "cookie_session=valor_do_cookie"
```

#### Teste de Análise

```bash
curl -X POST http://localhost/pr/diana/analisar \
  -H "Content-Type: application/json" \
  -H "Accept: application/json" \
  -b "cookie_session=valor_do_cookie" \
  -d '{
    "documentos_upload": [
      {
        "nome": "test.png",
        "conteudo_base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII="
      }
    ],
    "perguntas": [
      {
        "id": 1,
        "pergunta": "Qual é o material constitutivo?"
      }
    ],
    "contexto": {
      "cliente_segmento": "Industrial",
      "produto_descricao": "Produto teste",
      "ncm_atual": "12345678",
      "ncm_fornecedor": "87654321"
    }
  }'
```

## 🔍 Pontos de Verificação

### ✅ Configurações

- [ ] `diana_enabled = true` em config.php
- [ ] URLs e timeouts configurados
- [ ] Service carregando corretamente

### ✅ Banco de Dados

- [ ] Tabela `ctr_diana_logs` criada
- [ ] Campos corretos (id, id_empresa, id_usuario, etc.)
- [ ] Índices aplicados

### ✅ API Externa

- [ ] Endpoint `http://*************/gt/perguntas` acessível
- [ ] Aceita payload JSON conforme especificação
- [ ] Retorna estrutura de resposta esperada

### ✅ Permissões

- [ ] Flag `responder_perguntas_diana` em empresa
- [ ] Validação no controller funcionando
- [ ] Rejeição correta quando sem permissão

### ✅ Validações

- [ ] Rejeita payload sem arquivos
- [ ] Rejeita arquivos muito grandes
- [ ] Valida tipos de arquivo
- [ ] Retorna códigos de erro corretos

### ✅ Logs

- [ ] Salva requisições no banco
- [ ] Logs contêm metadados (sem base64 completo)
- [ ] Estatísticas funcionais

## 🐛 Troubleshooting

### Erro "Service não encontrado"

```
Causa: Diana_service não carrega
Solução: Verificar se arquivo existe em application/services/
```

### Erro "Tabela não existe"

```
Causa: Migração não executada
Solução: php index.php migration migrate
```

### Erro 403 "Sem permissão"

```
Causa: Empresa sem flag responder_perguntas_diana
Solução: Adicionar em Funções Adicionais da empresa
```

### Timeout na API DIANA

```
Causa: Rede ou endpoint indisponível
Verificação: Testar conectividade: curl http://*************/gt/perguntas
```

### Erro "Usuário não autenticado"

```
Causa: Sessão expirada
Solução: Fazer login na aplicação
```

## 📊 Resultados Esperados

### Controller de Teste

- **Taxa de sucesso:** >80%
- **Conectividade API:** ✅ (se na rede interna)
- **Estrutura banco:** ✅
- **Configurações:** ✅

### Postman Collection

- **Status do Serviço:** 200 OK
- **Análise Válida:** 200 OK com sugestões
- **Validações de Erro:** 400 Bad Request
- **Estatísticas:** 200 OK

### API DIANA Real

```json
{
  "respostas": [
    {
      "pergunta_id": 1,
      "resposta": "87654321",
      "documento_arquivo": "test.png",
      "documento_trecho": "",
      "documento_pagina": 1
    }
  ]
}
```

### Response Padronizada

```json
{
  "ok": true,
  "sugestoes": [
    {
      "id": 1,
      "pergunta": "",
      "resposta_sugerida": "87654321",
      "confianca": null,
      "referencias": {
        "pagina": 1,
        "trecho": "",
        "arquivos": [
          {
            "nome": "test.png",
            "page": 1
          }
        ]
      }
    }
  ]
}
```

## 🎯 Checklist de Validação

Antes de iniciar o frontend:

- [ ] Controller de teste executando sem erros críticos
- [ ] Migração aplicada com sucesso
- [ ] Pelo menos uma empresa configurada
- [ ] API DIANA respondendo (se na rede)
- [ ] Postman collection funcionando
- [ ] Logs sendo salvos na tabela
- [ ] Validações de erro corretas

## 📞 Próximos Passos

1. **Executar testes acima**
2. **Confirmar funcionalidade**
3. **Passar documentação para dev frontend:**
   - `FRONTEND_INTEGRATION_GUIDE.md`
   - `Especificacao-Tecnica-Analise-DIANA.md`
   - Prints do Figma
   - Collection Postman

**Backend 100% pronto! 🚀**
