<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

/**
 * Teste de Compatibilidade DIANA com CodeIgniter 2.x
 * 
 * Execute via: http://localhost/tests/diana_compatibility_test/run
 */
class Diana_compatibility_test extends CI_Controller
{
    private $test_results = [];
    private $test_count = 0;
    private $passed_count = 0;

    public function __construct()
    {
        parent::__construct();

        // Verificar se está em ambiente de desenvolvimento
        if (ENVIRONMENT !== 'development') {
            show_error('Testes só podem ser executados em ambiente de desenvolvimento', 403);
        }

        $this->load->helper(['url']);
    }

    /**
     * Executa testes de compatibilidade
     */
    public function run()
    {
        $this->output_header();

        $this->test_codeigniter_version();
        $this->test_input_methods();
        $this->test_diana_controller();
        $this->test_file_functions();

        $this->output_summary();
    }

    /**
     * Teste da versão do CodeIgniter
     */
    private function test_codeigniter_version()
    {
        $this->add_test_section("Versão do CodeIgniter");

        // Verificar versão
        $ci_version = CI_VERSION;
        $this->add_info("Versão detectada: {$ci_version}");

        $is_v2 = version_compare($ci_version, '3.0.0', '<');
        $this->assert_true($is_v2, "CodeIgniter versão 2.x detectada");

        if (!$is_v2) {
            $this->add_info("⚠️ Controller pode precisar de ajustes para CI 3.x+");
        }
    }

    /**
     * Teste dos métodos de Input
     */
    private function test_input_methods()
    {
        $this->add_test_section("Métodos de Input");

        // Verificar se métodos CI 3.x existem
        $has_is_get = method_exists($this->input, 'is_get');
        $this->assert_false($has_is_get, "is_get() NÃO existe (esperado no CI 2.x)");

        $has_is_post = method_exists($this->input, 'is_post');
        $this->assert_false($has_is_post, "is_post() NÃO existe (esperado no CI 2.x)");

        // Verificar se método server() existe
        $has_server = method_exists($this->input, 'server');
        $this->assert_true($has_server, "server() existe (usado no controller)");

        // Testar REQUEST_METHOD
        $request_method = $this->input->server('REQUEST_METHOD');
        $this->assert_not_empty($request_method, "REQUEST_METHOD disponível");
        $this->add_info("Método atual: {$request_method}");

        // Verificar se raw_input_stream existe
        $has_raw_input = property_exists($this->input, 'raw_input_stream');
        $this->assert_false($has_raw_input, "raw_input_stream NÃO existe (esperado no CI 2.x)");
    }

    /**
     * Teste do controller DIANA
     */
    private function test_diana_controller()
    {
        $this->add_test_section("Controller DIANA");

        // Verificar se arquivo existe
        $controller_file = APPPATH . 'controllers/pr/diana.php';
        $file_exists = file_exists($controller_file);
        $this->assert_true($file_exists, "Arquivo diana.php existe");

        if ($file_exists) {
            $content = file_get_contents($controller_file);

            // Verificar se usa métodos compatíveis
            $uses_server_method = strpos($content, "input->server('REQUEST_METHOD')") !== false;
            $this->assert_true($uses_server_method, "Usa server('REQUEST_METHOD') ao invés de is_get/is_post");

            $uses_file_get_contents = strpos($content, "file_get_contents('php://input')") !== false;
            $this->assert_true($uses_file_get_contents, "Usa file_get_contents('php://input') para raw input");

            // Verificar se não usa métodos incompatíveis
            $uses_is_get = strpos($content, 'is_get()') !== false;
            $this->assert_false($uses_is_get, "NÃO usa is_get() (incompatível com CI 2.x)");

            $uses_is_post = strpos($content, 'is_post()') !== false;
            $this->assert_false($uses_is_post, "NÃO usa is_post() (incompatível com CI 2.x)");

            $uses_raw_input_stream = strpos($content, 'raw_input_stream') !== false;
            $this->assert_false($uses_raw_input_stream, "NÃO usa raw_input_stream (incompatível com CI 2.x)");
        }
    }

    /**
     * Teste das funções de arquivo
     */
    private function test_file_functions()
    {
        $this->add_test_section("Funções de Arquivo");

        // Testar file_get_contents com php://input
        $php_input_available = function_exists('file_get_contents');
        $this->assert_true($php_input_available, "file_get_contents() disponível");

        // Testar json_decode
        $json_available = function_exists('json_decode');
        $this->assert_true($json_available, "json_decode() disponível");

        // Testar json_last_error
        $json_error_available = function_exists('json_last_error');
        $this->assert_true($json_error_available, "json_last_error() disponível");

        // Teste prático de JSON
        $test_json = '{"test": true}';
        $decoded = json_decode($test_json, true);
        $this->assert_true(json_last_error() === JSON_ERROR_NONE, "JSON decode funcional");
        $this->assert_true($decoded['test'] === true, "JSON parsing correto");
    }

    /**
     * Assertion helper
     */
    private function assert_true($condition, $message)
    {
        $this->test_count++;
        $passed = (bool) $condition;

        if ($passed) {
            $this->passed_count++;
        }

        $this->test_results[] = [
            'type' => 'test',
            'passed' => $passed,
            'message' => $message
        ];
    }

    /**
     * Assertion helper
     */
    private function assert_false($condition, $message)
    {
        $this->assert_true(!$condition, $message);
    }

    /**
     * Assertion helper
     */
    private function assert_not_empty($value, $message)
    {
        $this->assert_true(!empty($value), $message);
    }

    /**
     * Adiciona seção de teste
     */
    private function add_test_section($title)
    {
        $this->test_results[] = [
            'type' => 'section',
            'title' => $title
        ];
    }

    /**
     * Adiciona informação
     */
    private function add_info($message)
    {
        $this->test_results[] = [
            'type' => 'info',
            'message' => $message
        ];
    }

    /**
     * Saída do cabeçalho
     */
    private function output_header()
    {
        echo "<!DOCTYPE html><html><head><title>Teste Compatibilidade DIANA</title>";
        echo "<style>
            body { font-family: monospace; margin: 20px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 5px; }
            .section { margin: 20px 0; padding: 10px; background: #e8f4fd; border-left: 4px solid #2196F3; }
            .test-pass { color: #4CAF50; }
            .test-fail { color: #f44336; }
            .info { color: #666; font-style: italic; }
            .summary { margin: 20px 0; padding: 15px; background: #f0f0f0; border-radius: 3px; }
            h1 { color: #333; text-align: center; }
        </style></head><body>";
        echo "<div class='container'>";
        echo "<h1>🔧 Teste de Compatibilidade DIANA - CodeIgniter 2.x</h1>";
        echo "<p><strong>CodeIgniter:</strong> " . CI_VERSION . " | <strong>PHP:</strong> " . PHP_VERSION . "</p>";
    }

    /**
     * Saída do resumo
     */
    private function output_summary()
    {
        // Processar resultados
        foreach ($this->test_results as $result) {
            switch ($result['type']) {
                case 'section':
                    echo "<div class='section'><strong>{$result['title']}</strong></div>";
                    break;
                case 'test':
                    $class = $result['passed'] ? 'test-pass' : 'test-fail';
                    $icon = $result['passed'] ? '✅' : '❌';
                    echo "<div class='{$class}'>{$icon} {$result['message']}</div>";
                    break;
                case 'info':
                    echo "<div class='info'>ℹ️ {$result['message']}</div>";
                    break;
            }
        }

        // Resumo final
        $success_rate = $this->test_count > 0 ? round(($this->passed_count / $this->test_count) * 100, 1) : 0;
        $status = $success_rate >= 90 ? '🎉 COMPATÍVEL' : '⚠️ PRECISA AJUSTES';

        echo "<div class='summary'>";
        echo "<h3>{$status}</h3>";
        echo "<p><strong>Total de testes:</strong> {$this->test_count}</p>";
        echo "<p><strong>Aprovados:</strong> {$this->passed_count}</p>";
        echo "<p><strong>Taxa de sucesso:</strong> {$success_rate}%</p>";

        if ($success_rate >= 90) {
            echo "<p style='color: #4CAF50;'><strong>✅ Controller DIANA compatível com CodeIgniter 2.x</strong></p>";
            echo "<p>Agora você pode testar os endpoints no Postman!</p>";
        } else {
            echo "<p style='color: #f44336;'><strong>❌ Problemas de compatibilidade detectados</strong></p>";
        }

        echo "</div>";

        echo "<hr><p><small>Para testar endpoints: <a href='" . site_url('pr/diana/status') . "'>Status DIANA</a></small></p>";
        echo "</div></body></html>";
    }
}
