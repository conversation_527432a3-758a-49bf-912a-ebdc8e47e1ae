# Guia de Integração Frontend - DIANA Responder Perguntas

## 📋 Resumo

Este documento contém todas as informações necessárias para implementar o frontend da funcionalidade **DIANA auxilia cliente a responder perguntas**.

## 🎯 Objetivo

Implementar componentes Vue.js 2 para:

- Área de drag & drop para upload de documentos
- Botão "Análise do DIANA" que processa documentos
- Preenchimento automático de respostas baseado na análise
- Exibição de referências (página, trecho, arquivos)

## 🏗️ Arquitetura Frontend

### Componentes a Implementar

1. **`DianaDropzone.vue`** - Componente de upload
2. **Modificações em `Default.vue`** - Integração principal
3. **Modificações em `Item.vue`** - Exibição de resultados

### Fluxo de Funcionamento

```
1. Usuário seleciona arquivos → DianaDropzone
2. Clica "Análise do DIANA" → Default.vue
3. Envia para backend → POST /pr/diana/analisar
4. Recebe sugestões → Default.vue processa
5. Preenche respostas → Item.vue exibe
6. Usuário revisa e salva → Fluxo existente
```

## 🔌 API Backend Disponível

### Endpoint Principal

**`POST /pr/diana/analisar`**

**Request:**

```json
{
  "documentos_upload": [
    {
      "nome": "arquivo.pdf",
      "conteudo_base64": "base64_string_do_arquivo"
    }
  ],
  "perguntas": [
    {
      "id": 1,
      "pergunta": "Qual é o material constitutivo?"
    }
  ],
  "contexto": {
    "cliente_segmento": "Industrial",
    "produto_descricao": "Descrição do produto",
    "ncm_atual": "12345678",
    "ncm_fornecedor": "87654321"
  }
}
```

**Response (Sucesso):**

```json
{
  "ok": true,
  "sugestoes": [
    {
      "id": 1,
      "pergunta": "",
      "resposta_sugerida": "Aço inoxidável",
      "confianca": null,
      "referencias": {
        "pagina": 2,
        "trecho": "Material: aço inoxidável AISI 304",
        "arquivos": [
          {
            "nome": "arquivo.pdf",
            "page": 2
          }
        ]
      }
    }
  ]
}
```

**Response (Erro):**

```json
{
  "ok": false,
  "codigo": "INVALID_PAYLOAD",
  "mensagem": "É necessário enviar pelo menos um arquivo"
}
```

### Endpoints Auxiliares

- **`GET /pr/diana/status`** - Verificar se serviço está ativo
- **`GET /pr/diana/estatisticas`** - Estatísticas de uso (opcional)

### Códigos de Erro

| Código            | Descrição                                |
| ----------------- | ---------------------------------------- |
| `DISABLED`        | Funcionalidade desabilitada para empresa |
| `NO_FILES`        | Nenhum arquivo enviado                   |
| `INVALID_PAYLOAD` | Dados inválidos                          |
| `TIMEOUT`         | Timeout na API DIANA                     |
| `DIANA_ERROR`     | Erro na API externa                      |

## 🎨 Especificação de Componentes

### 1. DianaDropzone.vue

**Props:**

```javascript
props: {
  maxFileSizeMb: { type: Number, default: 10 },
  maxTotalSizeMb: { type: Number, default: 30 },
  accepted: { type: String, default: '.pdf,.docx,.jpg,.jpeg,.png' },
  disabled: { type: Boolean, default: false }
}
```

**Events:**

```javascript
// Quando arquivos são selecionados/alterados
this.$emit("change", files); // Array de File objects

// Quando arquivo é removido
this.$emit("remove", index);
```

**Funcionalidades:**

- ✅ Drag & drop visual conforme Figma
- ✅ Seleção por clique
- ✅ Lista de arquivos com opção de remoção
- ✅ Validação de tipos (PDF, DOCX, JPG, JPEG, PNG)
- ✅ Validação de tamanhos (individual e total)
- ✅ Estados de loading/disabled

### 2. Modificações em Default.vue

**Novas propriedades de data:**

```javascript
data() {
  return {
    // ... dados existentes ...

    // DIANA
    dianaFiles: [],
    dianaLoading: false,
    dianaEnabled: false, // vem do PHP via prop
    dianaResults: new Map() // ID da pergunta → dados da sugestão
  }
}
```

**Novo método principal:**

```javascript
async analisarDiana() {
  if (this.dianaFiles.length === 0) {
    // Exibir mensagem de erro
    return;
  }

  this.dianaLoading = true;

  try {
    // 1. Converter arquivos para base64
    const documentos = await this.convertFilesToBase64(this.dianaFiles);

    // 2. Extrair perguntas do estado atual
    const perguntas = this.perguntas.map(item => ({
      id: item.id || item.id_pergunta,
      pergunta: item.pergunta
    }));

    // 3. Montar payload
    const payload = {
      documentos_upload: documentos,
      perguntas: perguntas,
      contexto: this.buildContexto() // opcional
    };

    // 4. Fazer requisição
    const response = await this.callDianaAPI(payload);

    // 5. Processar resultados
    this.processarSugestoes(response.sugestoes);

  } catch (error) {
    // Tratar erro
    console.error('Erro na análise DIANA:', error);
  } finally {
    this.dianaLoading = false;
  }
}
```

**Métodos auxiliares necessários:**

```javascript
convertFilesToBase64(files) {
  return Promise.all(
    files.map(file => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          resolve({
            nome: file.name,
            conteudo_base64: reader.result.split(',')[1] // Remove data:type;base64,
          });
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    })
  );
}

callDianaAPI(payload) {
  return this.$http.post('/pr/diana/analisar', payload);
}

processarSugestoes(sugestoes) {
  sugestoes.forEach(sugestao => {
    if (sugestao.resposta_sugerida) {
      // Encontrar pergunta correspondente
      const pergunta = this.perguntas.find(p =>
        (p.id || p.id_pergunta) === sugestao.id
      );

      if (pergunta) {
        // Preencher resposta
        pergunta.resposta = sugestao.resposta_sugerida;
        pergunta.pendente = 0; // Marcar como respondida

        // Adicionar metadados DIANA
        pergunta.dianaReferencia = sugestao.referencias;
        pergunta.dianaArquivos = sugestao.referencias.arquivos;
      }

      // Armazenar para referência
      this.dianaResults.set(sugestao.id, sugestao);
    }
  });
}
```

### 3. Modificações em Item.vue

**Exibição de referências DIANA:**

```html
<!-- Adicionar após o label da pergunta -->
<div v-if="item.dianaReferencia" class="diana-info">
  <small class="text-muted">
    <i class="fa fa-robot" :title="dianaTooltip"></i>
    Sugestão DIANA
  </small>
</div>

<!-- Adicionar antes do textarea -->
<div
  v-if="item.dianaArquivos && item.dianaArquivos.length > 0"
  class="diana-files"
>
  <strong>Arquivo:</strong>
  <span v-for="(arquivo, index) in item.dianaArquivos" :key="index">
    <a href="#" @click="viewDianaFile(arquivo)" class="diana-file-link">
      {{ arquivo.nome }}
      <span v-if="arquivo.page">(p. {{ arquivo.page }})</span>
    </a>
    <span v-if="index < item.dianaArquivos.length - 1">, </span>
  </span>
</div>
```

**Computed para tooltip:**

```javascript
computed: {
  dianaTooltip() {
    if (!this.item.dianaReferencia) return '';

    const ref = this.item.dianaReferencia;
    let tooltip = 'Sugestão DIANA';

    if (ref.pagina) tooltip += `\nPágina: ${ref.pagina}`;
    if (ref.trecho) tooltip += `\nTrecho: "${ref.trecho}"`;

    return tooltip;
  }
}
```

## 📱 Layout/UX Conforme Figma

### Posicionamento

- **Dropzone + Botão:** Lado direito do seletor de partnumbers (topo da tela)
- **Layout:** Dropzone à esquerda, botão "Análise do DIANA" à direita
- **Responsividade:** Stack verticalmente em telas menores

### Estados Visuais

- **Normal:** Dropzone tracejada, botão azul
- **Dragging:** Dropzone destacada
- **Loading:** Overlay com spinner + texto "Analisando documentos..."
- **Disabled:** Quando sem permissão ou service offline

### Cores/Ícones

- **Botão:** Bootstrap primary (azul)
- **Ícones:** Font Awesome (fa-upload, fa-robot, fa-file)
- **Estados:** Success (verde), Warning (amarelo), Error (vermelho)

## 🔧 Validações Frontend

### Antes de Enviar

```javascript
validarAntes() {
  if (this.dianaFiles.length === 0) {
    this.showError('Selecione pelo menos um arquivo para análise');
    return false;
  }

  if (this.perguntas.length === 0) {
    this.showError('Nenhuma pergunta disponível para análise');
    return false;
  }

  // Validar tamanhos
  const totalSize = this.dianaFiles.reduce((sum, file) => sum + file.size, 0);
  const maxTotal = this.maxTotalSizeMb * 1024 * 1024;

  if (totalSize > maxTotal) {
    this.showError(`Tamanho total excede ${this.maxTotalSizeMb}MB`);
    return false;
  }

  return true;
}
```

### Tipos de Arquivo

```javascript
const allowedTypes = [
  "application/pdf",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "image/jpeg",
  "image/png",
];

const allowedExtensions = [".pdf", ".docx", ".jpg", ".jpeg", ".png"];
```

## 💾 Integração com Salvamento

### Anexar Arquivos ao Salvar

```javascript
// No método de salvar existente, adicionar:
if (this.dianaFiles.length > 0) {
  // Adicionar arquivos DIANA aos attachments existentes
  const perguntasRespondidas = this.perguntas.filter(
    (p) => p.resposta && p.resposta.trim()
  );

  perguntasRespondidas.forEach((pergunta) => {
    // Relacionar arquivos DIANA com perguntas respondidas
    this.attachments[pergunta.id] = this.dianaFiles;
  });
}
```

## 🔒 Controle de Permissões

### Verificação no Frontend

```javascript
// Em Default.vue, receber do PHP:
props: {
  // ... props existentes ...
  dianaEnabled: {
    type: Boolean,
    default: false
  }
}

// Renderizar condicionalmente:
<diana-dropzone v-if="dianaEnabled" />
<button v-if="dianaEnabled" @click="analisarDiana">Análise do DIANA</button>
```

### No PHP (responder.php)

```php
// Adicionar no controller:
$empresa = $this->empresa_model->get_entry_by_id(sess_user_company());
$funcoes = explode('|', $empresa->funcoes_adicionais ?? '');
$data['diana_enabled'] = in_array('responder_perguntas_diana', $funcoes);
```

## 🧪 Testes Frontend

### Casos de Teste

1. **Upload de arquivos válidos** - PDF, DOCX, JPG, PNG
2. **Validação de tamanhos** - Individual e total
3. **Análise com sucesso** - Preenchimento automático
4. **Erros de API** - Timeout, payload inválido
5. **Estados de loading** - Visual feedback
6. **Permissões** - Com/sem acesso

### Dados de Teste

```javascript
// Arquivo de teste pequeno (PNG 1x1):
const testFile =
  "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII=";

// Payload de teste:
const testPayload = {
  documentos_upload: [{ nome: "test.png", conteudo_base64: testFile }],
  perguntas: [{ id: 1, pergunta: "Teste?" }],
};
```

## 📚 Dependências Existentes

### Já Disponíveis no Projeto

- ✅ Vue.js 2
- ✅ Bootstrap
- ✅ vue-select
- ✅ Lodash
- ✅ Font Awesome
- ✅ vue-loading-overlay

### Estrutura de Arquivos

```
assets/vuejs/src/components/PerguntasRespostas/Resposta/
├── Default.vue (modificar)
├── Item.vue (modificar)
└── DianaDropzone.vue (criar)
```

## 🚀 Passos para Implementação

### 1. Preparação

- [ ] Executar migração: `php index.php migration migrate`
- [ ] Configurar empresa de teste com `responder_perguntas_diana`
- [ ] Testar backend com Postman

### 2. Desenvolvimento

- [ ] Criar `DianaDropzone.vue`
- [ ] Modificar `Default.vue` - integração
- [ ] Modificar `Item.vue` - exibição resultados
- [ ] Implementar validações
- [ ] Adicionar loading states

### 3. Testes

- [ ] Testar upload e validações
- [ ] Testar integração com API
- [ ] Testar preenchimento automático
- [ ] Testar salvamento com anexos

## ❓ Dúvidas Frequentes

**Q: Como funciona o mapeamento pergunta → resposta?**
A: O campo `id` na resposta da DIANA corresponde ao `id` ou `id_pergunta` da pergunta no frontend.

**Q: O que fazer se a DIANA não retornar resposta para uma pergunta?**
A: A pergunta permanece vazia, usuário preenche manualmente.

**Q: Como tratar timeouts?**
A: API retorna código `TIMEOUT`, exibir mensagem amigável orientando reduzir arquivos.

**Q: Arquivos ficam persistidos onde?**
A: Após "Salvar", vão para `assets/respostas/` seguindo o fluxo atual de anexos.

---

## 📞 Suporte

Para dúvidas sobre a implementação:

1. Consultar especificação técnica completa
2. Testar endpoints com Postman collection fornecida
3. Verificar logs da tabela `ctr_diana_logs`
