<?php

class Migration_Create_Table_Ctr_Diana_Logs extends CI_Migration
{
    private $_table = "ctr_diana_logs";

    public function up()
    {
        if (!$this->db->table_exists($this->_table)) {
            $fields = array(
                'id' => array(
                    'type'           => 'int',
                    'constraint'     => 11,
                    'unsigned'       => true,
                    'auto_increment' => true
                ),
                'id_empresa' => array(
                    'type'       => 'int',
                    'constraint' => 11,
                    'unsigned'   => true,
                    'null'       => false
                ),
                'id_usuario' => array(
                    'type'       => 'int',
                    'constraint' => 11,
                    'unsigned'   => true,
                    'null'       => false
                ),
                'created_at' => array(
                    'type' => 'datetime',
                    'null' => false
                ),
                'status_code' => array(
                    'type'       => 'int',
                    'constraint' => 11,
                    'null'       => true
                ),
                'ok' => array(
                    'type'       => 'tinyint',
                    'constraint' => 1,
                    'default'    => 0,
                    'null'       => false
                ),
                'request_json' => array(
                    'type' => 'text',
                    'null' => true
                ),
                'response_json' => array(
                    'type' => 'longtext',
                    'null' => true
                ),
                'erro' => array(
                    'type'       => 'varchar',
                    'constraint' => 255,
                    'null'       => true
                )
            );

            $this->dbforge->add_field($fields);
            $this->dbforge->add_key('id', true);
            $this->dbforge->add_key(['id_empresa', 'created_at']);
            $this->dbforge->create_table($this->_table);
        }
    }

    public function down()
    {
        if ($this->db->table_exists($this->_table)) {
            $this->dbforge->drop_table($this->_table);
        }
    }
}
