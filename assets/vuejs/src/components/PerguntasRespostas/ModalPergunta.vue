<template>
    <div class="modal fade" id="perguntasRespostas" tabindex="-1" role="dialog" aria-labelledby="perguntasRespostas" aria-hidden="true">
        <loading 
            :active.sync="isLoading" 
            :is-full-page="fullPage"
        >
        </loading>

        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content" style="overflow-y: auto; max-height: 600px;">
                <div class="modal-header">
                    <h3 class="modal-title" style="display: inline !important" id="exampleModalLabel">Perguntar ao Usuário </h3>

                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="margin-top: 6px">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <div class="alert alert-danger alert-dismissable" v-if="isInvalid">
                        <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                        <h4>Ops... alguns erros aconteceram</h4>
                        <ul>
                            <li v-for="error in errors" :key="error">{{ error }}</li>
                        </ul>
                    </div>

                    <div class="row">
                        <div class="col-md-4" >
                            <label 
                                for="nome" 
                                class="d-block"
                            >
                                Grupo de Perguntas 
                            </label>
                            <v-select 
                                v-model="selectedPerguntasGrupo" 
                                @search="fetchPerguntasGrupos" 
                                @input="handlePerguntas" 
                                :options="perguntasGrupos"
                                class="grupo-perguntas overflow-liberado"
                                :disabled="!permissao_grupo_perguntas"
                            >   
                                <div slot="no-options" name="no-options">Não encontramos nenhuma opção.</div>
                            </v-select>
                        </div>

                        <div class="col-md-4">
                            <label 
                                for="nome" 
                                class="d-block"
                            >
                                Part Numbers {{ selectedPartnumbers.length ? '(' + selectedPartnumbers.length + ')' : '' }}
                            </label>
                            <v-select 
                                v-model="selectedPartnumbers" 
                                @search="fetchPartnumbers" 
                                @input="selectedPartnumbers.length > 0 ? (userType === 'owner' ? buscarOwner() : buscarResponsavel()) : selectedOwners = null"
                                :multiple="true" :options="partnumbers"
                                :class="{'scrollable-select': this.selecteds && selectedPartnumbers.length > 20}"
                            >
                                <div slot="no-options" name="no-options">Não encontramos nenhuma opção.</div>
                            </v-select>
                        </div>

                        <div v-if="hasOwner">
                            <div class="col-md-4">
                                <label for="userType">Tipo de Responsável:</label>
                                <div>
                                    <input type="radio" id="user" value="user" v-model="userType" />
                                    <label for="user" class="radio-input">Usuário</label>
                                    <input type="radio" id="owner" value="owner" v-model="userType" />
                                    <label for="owner" class="radio-input">Owner</label>
                                </div>
                            </div>
                        </div>

                        <div v-else>
                            <div class="col-md-4" v-if="userType === 'user'">
                                <label 
                                    for="nome" 
                                    class="d-block"
                                >
                                    Responsável
                                </label>
                                <v-select multiple data-selected-text-format="count > 1" data-count-selected-text="Responsáveis ({0})" v-model="selectedResponsaveis" :options="responsaveis">
                                    <div slot="no-options" name="no-options">Não encontramos nenhuma opção.</div>
                                    <template #option="option">
                                        <div style="padding-left: 12px;">
                                            <div v-if="option.isFirstActive">
                                                <span class="label label-success" style="font-size: 12px; margin-left: -12px; border-radius: 10px;">Usuários ativos</span>
                                            </div>
                                            <div v-if="option.isFirstInactive">
                                                <span class="label label-danger" style="font-size: 12px; margin-left: -12px; border-radius: 10px;">Usuários inativos</span>
                                            </div>
                                            {{ option.label }} ({{ option.email }})
                                        </div>
                                    </template>
                                </v-select>
                            </div>
                        </div>
                    </div>
                    <div class="row" style="margin-top: 12px">
                        <div v-if="hasOwner">
                        
                            <div class="col-md-4"  v-if="userType === 'user'">
                                <label 
                                    for="nome" 
                                    class="d-block"
                                >
                                    Responsável
                                </label>
                                <v-select multiple data-selected-text-format="count > 1" data-count-selected-text="Responsáveis ({0})" v-model="selectedResponsaveis" :options="responsaveis">
                                    <div slot="no-options" name="no-options">Não encontramos nenhuma opção.</div>
                                    <template #option="option">
                                        <div style="padding-left: 12px;">
                                            <div v-if="option.isFirstActive">
                                                <span class="label label-success" style="font-size: 12px; margin-left: -12px; border-radius: 10px;">Usuários ativos</span>
                                            </div>
                                            <div v-if="option.isFirstInactive">
                                                <span class="label label-danger" style="font-size: 12px; margin-left: -12px; border-radius: 10px;">Usuários inativos</span>
                                            </div>
                                            {{ option.label }} ({{ option.email }})
                                        </div>
                                    </template>
                                </v-select>
                            </div>

                            <div class="col-md-4" v-if="userType === 'owner'">
                                <label for="selectedOwners" class="d-block">
                                    Owners
                                </label>
                                
                                <v-select 
                                    data-live-search="true" 
                                    class="owners overflow-liberado" v-model="selectedOwners" :options="owners">
                                    <div slot="no-options" name="no-options">Não encontramos nenhuma opção.</div>
                                    <template #option="owner">
                                        {{owner.label}}
                                    </template>
                                </v-select>
                            </div>

                        </div>
                        <div v-else>
                            <div class="col-md-4" >&nbsp;</div>
                        </div>

                        <div class="col-md-6">
                            <div v-if="selectedPartnumbers.length > 0">
                                <a class="btn btn-primary" role="button" style="margin-top: 23px;" data-toggle="collapse" href="#collapseDescPartnumber" aria-expanded="false" aria-controls="collapseDescPartnumber">
                                    <i class="glyphicon glyphicon-info-sign"> </i> Descrição Part Number(s)
                                </a>
                                
                                <div class="collapse" id="collapseDescPartnumber">
                                    <div class="lista-partnumbers">
                                        <ul>
                                            <li v-for="partnumber in selectedPartnumbers" :key="partnumber.key">
                                                <strong>{{ partnumber.label }}</strong> - {{ partnumber.item.descricao }}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <hr />

                    <div class="row">
                        <div class="col-sm-6">
                            <ModalItemInf @handleClick="novaPergunta"></ModalItemInf>
                        </div>
                    </div>
                    <hr />
                    <div class="row">
                        <div class="col-sm-12">
                            <ModalListagemPerguntas ref="listagemPerguntas" @updatePerguntas="updatePerguntas" :perguntas="perguntas" :base-url="baseUrl"/>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="btn btn-default" @click="handleCancelar" data-dismiss="modal" aria-label="Close">
                        Cancelar
                    </button>
                    <button class="btn btn-primary" @click="handleSalvar">
                        <i class="glyphicon glyphicon-send"></i> Enviar
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import 'vue-loading-overlay/dist/vue-loading.css';
import Loading from 'vue-loading-overlay';
import _ from 'lodash';
import vSelect from 'vue-select';
import ModalListagemPerguntas from './ModalListagemPerguntas';
import ModalItemInf from './ModalItemInf';

export default {
    data() {
        return {
            carregando_Grupos: false,
            carregando_Partnumbers: false,
            carregando_Responsaveis: false,
            carregando_Owners: false,
            carregando_Responsavel: false,
            carregando_Owner: false,
            selecteds: false,
            grupoPerguntasAutocomplete: '',
            responsavelAutocomplete: '',
            partNumberAutocomplete: '',
            perguntasGrupos: [],
            selectedPerguntasGrupo:  '',
            partnumbers: [],
            selectedPartnumbers:  '',
            responsaveis: [],
            selectedResponsaveis:  '',
            responsavelSelected: '',
            perguntas: [],
            errors: [],
            isInvalid: false,
            isLoading: false,
            fullPage: true,
            userType: 'user',
            selectedOwners: [],
            owners: [],
            loadingOwner: false,
            showModal: true,
            permissao_grupo_perguntas: 0
        }
    },
    props: {
        baseUrl: {
            required: true,
            type: String
        },
        hasOwner: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        async permissaoGrupoPerguntas() {
            let response = await this.$http.get('permissao/permissaoGrupoPerguntas', {});
            if (response.data)
            {
                this.permissao_grupo_perguntas = response.data;
            } else {
                this.permissao_grupo_perguntas = 0;
            }

        },
        novaPergunta(item) {
            this.$refs.listagemPerguntas.pushPergunta({
                ...item, id: Math.random()
            });

        },
        getDescPartNumbers() {
            return this.$http.get('pr/perguntas/getDescPartNumbers', { 
                params: {
                    partnumbers: this.selectedPartnumbers.map(partnumber => partnumber.key)
                } 
            });
        },
       
        fetchPartnumberDescriptions() {
            

            this.selectedPartnumbers.forEach(partnumber => {
                
            });
        },
        updatePerguntas(items) {
            this.perguntas = items;
        },
        getGrupos() {
            return this.$http.get('pr/perguntas/getGrupos', {
                params: {
                    search: this.grupoPerguntasAutocomplete
                }
            });
        },
        getPartnumbers() {
            return this.$http.get('pr/perguntas/getPartnumbers', {
                params: {
                    search: this.partNumberAutocomplete
                }
            });
        },
        getResponsaveis() {
            return this.$http.get('pr/perguntas/getResponsaveis');
        },
        getPerguntas() {
            return this.$http.get('pr/perguntas/getPerguntasGrupo/'+this.selectedPerguntasGrupo.key);
        },

        getOwner() {
            return this.$http.post('pr/perguntas/getOwnerPartnumber', {
                partnumbers: this.selectedPartnumbers.map(item => { return item.key })
            })
        },
        getOwners() {
            return this.$http.get('pr/perguntas/getOwners');
        },
        getOwnerOfUserLogged() {
            return this.$http.get('pr/perguntas/getOwnerOfUserLogged');
        },
        validateFields() {
            this.errors = [];
            
            let perguntas = this.$refs.listagemPerguntas.getPerguntas(); 

            let emptyPerguntas = _.isEmpty(perguntas); 
            let emptyPartNumbers = _.isEmpty(this.selectedPartnumbers); 
            let emptyResponsavel = _.isEmpty(this.selectedResponsaveis);
            let emptyOwners = _.isEmpty(this.selectedOwners); 

            if (emptyPerguntas) {
                this.errors.push("O campo Grupo de Perguntas é obrigatório.");
            }

            if (emptyPartNumbers) {
                this.errors.push("O campo Part Number é obrigatório.");
            }

            if (this.userType === 'user' && emptyResponsavel) {
                this.errors.push("O campo Responsável é obrigatório.");
            }

            if (this.userType === 'owner' && emptyOwners) {
                this.errors.push("O campo Owners é obrigatório.");
            }

            if (!_.isEmpty(this.errors)) {
                this.isInvalid = true;
                return false;
            }

            this.isInvalid = false;
            return true;
        },
        handleSalvar() {
            let valid = this.validateFields();

            if (!valid) {
                return false;
            }

            this.isLoading = true;

            this.$http.post('pr/perguntas/salvarPerguntas', {
                partnumbers: this.selectedPartnumbers.map(({item}) => { return item }),
                perguntas: this.$refs.listagemPerguntas.getPerguntas(),
                grupoPerguntas: this.selectedPerguntasGrupo,
                responsavel: this.selectedResponsaveis,
                tipoResponsavel: this.userType,
                owner: this.selectedOwners,
            }).then(({data}) => {
                this.isLoading = false;
                if (data.errors.length) {
                    var msg = 'As perguntas foram salvas. <br>';
                    for (let i = 0; i < data.errors.length; i++) {
                        msg += data.errors[i] + "<br>";    
                    }

                    swal("Sucesso!", msg, "warning");
                setTimeout(function(){  
                // window.location.reload(); 
                    this.showModal = false;
                }, 2000);
                } else {
                    swal("Sucesso!", data.msg, "success");
                setTimeout(function(){  
                    // window.location.reload(); 
                     this.showModal = false;
                }, 1500);
                   
                }

                $('#perguntasRespostas').modal('hide');

                this.selectedResponsaveis = false;
            }).catch((err) => {
                this.isLoading = false;
                swal("Erro!", "Algo inesperado aconteceu. Contate um administrador.", "error");

                $('#perguntasRespostas').modal('hide');
            });
        },
        handleCancelar(){
           
            this.isLoading = true;
            
            this.$http.get('pr/perguntas/unsetUsuarioBloqueadorItens').then(({data}) => {
                this.isLoading = false;
                this.showModal = false;
            }).catch((err) => {
                this.isLoading = false;
                swal("Erro!", "Algo inesperado aconteceu. Contate um administrador.", "error");

                $('#perguntasRespostas').modal('hide');
            });
            
        },
        buscarResponsavel(){

            const requestData = this.selectedPartnumbers.map(({ key, item }) => {
                return {
                    partnumber: key,
                    estabelecimento: item.estabelecimento
                };
            });

            return this.$http.post('pr/perguntas/getResponsavelPartnumber', {
                partnumbers: requestData.map(item => item.partnumber),
                estabelecimentos: requestData.map(item => item.estabelecimento)
            }).then(({data}) => {
                this.isLoading = false;
                if (data.error) {
                    var msg = data.msg;
                    swal("Atenção!", msg, "warning");
                }else{
                    if (!_.isEmpty(data.data)) {
                        const usuario = data.data;
                        
                        let responsaveis = this.responsaveis.filter((item) => {
                            return item.key == usuario.id_usuario;
                        });

                        this.selectedResponsaveis = responsaveis.shift();
                        this.carregando_Responsavel = true;
                        this.is_loading();
                    }
                }
            });
           
           
        },
        async buscarOwner() {
            // this.isLoading = true;
            const { data } = await this.getOwner();

            if (!_.isEmpty(data.data)) {
                const owner = data.data;

                let owners = this.owners.filter((item) => {
                    return item.key == owner.id_owner;
                });

                if (owners.length == 0) {
                    this.userType = 'user';
                }
                

                this.selectedOwners = owners.shift();

                this.carregando_Owner = true;
                this.is_loading();
            }
        },
        // async buscarOwnerOfUserLogged() {
        //     this.loadingOwner = true;
        //     const { data } = await this.getOwnerOfUserLogged();

        //     if (!_.isEmpty(data.data)) {
        //         const userOfOwner = data.data;
        //         // console.log(userOfOwner);

        //         let ownerOfUser = this.owners.filter((item) => {
        //             return item.key == userOfOwner.id_owner;
        //         });

        //         // console.log('buscou os owners do usuário logado');
        //         this.loadingOwner = false;

        //         this.selectedOwners = ownerOfUser.shift();
        //     }
        // },
        async handlePerguntas() {
            const { data } = await this.getPerguntas();

            this.perguntas = data.data.map((item) => {
                return item;
            });
        
            await this.fetchPartnumberDescriptions();

            this.$refs.listagemPerguntas.pushPerguntas(this.perguntas);
        },
        async requestPartnumbers() {

            this.selecteds =  document.querySelectorAll("#itens_holder td > input:checked").length > 0 ? true : false;

            if (this.selecteds && this.partnumbers.length > 0 && _.isEmpty(this.partNumberAutocomplete)) {
                this.carregando_Partnumbers = true;
                this.is_loading();
                return this.partnumbers;
            }
            const { data } = await this.getPartnumbers();

            this.partnumbers = data.data.map((item) => {
                return {
                    key: item.part_number,
                    label: item.part_number,
                    item: item
                };
            })
            this.carregando_Partnumbers = true;
            this.is_loading();
        },
        async is_loading() {
            if (this.carregando_Grupos && this.carregando_Partnumbers && 
            this.carregando_Responsaveis && this.carregando_Owners && 
            this.carregando_Responsavel && this.carregando_Owner )
            {
                this.isLoading = false;
            }
        },
        async requestGrupos() {
            if (this.selecteds && this.perguntasGrupos.length > 0) {
                this.carregando_Grupos = true;
                this.is_loading();
                return this.perguntasGrupos;
            }

            const { data } = await this.getGrupos();
            $('.v-select.grupo-perguntas').closest('.col-md-4').addClass('pos-unset');
            $('.v-select.grupo-perguntas').closest('.row').addClass('pos-relative');
            this.perguntasGrupos = data.data.map((item) => {
                return {
                    key: item.id,
                    label: item.nome,
                    item: item
                };
            })
            this.carregando_Grupos = true;
            this.is_loading();
        },
        async requestResponsaveis() {
            this.carregando_Responsaveis = true;
            if (this.selecteds && this.responsaveis.length > 0) {
                // this.carregando_Responsaveis = true;
                this.is_loading();
                 return this.responsaveis;
            }

            const { data } = await this.getResponsaveis();

            const usuariosAtivos = data.data.usuarios_ativos.map((item, idx) => {
                return {
                    key: item.id_usuario,
                    label: item.nome,
                    email: item.email,
                    item: item,
                    isFirstActive: idx === 0
                };
            });

            const usuariosInativos = data.data.usuarios_inativos.map((item, idx) => {
                return {
                    key: item.id_usuario,
                    label: item.nome,
                    email: item.email,
                    item: item,
                    isFirstInactive: idx === 0
                };
            });

            this.responsaveis = [
                ...usuariosAtivos,
                ...usuariosInativos
            ]
            this.carregando_Responsaveis = true;
            this.is_loading();
        },
        async fetchPerguntasGrupos(search, loading) {
            this.grupoPerguntasAutocomplete = search;
            loading(true);
            await this.requestGrupos();
            loading(false);
           
        },
        async fetchPartnumbers(search, loading) {
            this.partNumberAutocomplete = search;
            loading(true);
            await this.requestPartnumbers();
            loading(false);
        },
        async buscarOwners() {

            if (this.selecteds && this.owners.length > 0) {
                this.carregando_Owners = true;
                this.is_loading();
                return this.owners;
            }
    
            const { data } = await this.getOwners();

            this.owners = data.data.map((item) => {
                return {
                    key: item.id_owner,
                    codigo: item.codigo,
                    nomes: item.nomes,
                    // label: item.descricao,
                    label: item.codigo + ' - ' + item.descricao + ' - ' + item.nomes,
                    emails: item.emails,
                };
            });
            this.carregando_Owners = true;
            this.is_loading();

            $('.v-select.owners').closest('.col-md-4').addClass('pos-unset');
            $('.v-select.owners').closest('.row').addClass('pos-relative');
        },

        // Método para verificar se a empresa tem o campo owner habilitado e não depende do controller
        checkHasOwner() {
            this.$http.get('pr/perguntas/checkHasOwner').then(({data}) => {
                this.hasOwner = data.hasOwner;
            });
        }

    },
    mounted() {
        this.isLoading = true;
        let self = this;

        if (self.hasOwner) {
            self.userType = 'owner';
        } else {
            self.userType = 'user';
        };
        
        //  this.selectedPerguntasGrupo = this.selectedPerguntasGrupo == false ? '' : this.selectedPerguntasGrupo;
        $('#perguntasRespostas').on('shown.bs.modal', async function () {
            this.isLoading = true;
            self.requestGrupos();
            self.requestPartnumbers();
            self.requestResponsaveis();
            self.buscarOwners();

            let partNumbers = [];
            let partNumbersEstabelecimentos = [];
           $(".item_selected:checked").each((index, item) => {
                let tipo = $(item).attr('data-tipo');

                if (tipo == 'homologacao')
                {
                    partNumbersEstabelecimentos.push($(item).attr('data-estabelecimento'));
                    partNumbers.push($(item).attr('data-part-number'));

                } else {
                    partNumbers.push(item.value);
                    partNumbersEstabelecimentos.push(item.dataset.estabelecimento); 
                }
            });

            let infoModalPerguntas = localStorage.getItem("infoModalPerguntas");
            let infoModalPerguntasEstabelecimento = localStorage.getItem("infoModalPerguntasEstabelecimento");   

            localStorage.removeItem("infoModalPerguntas");
            localStorage.removeItem("infoModalPerguntasEstabelecimento");

            if (!_.isEmpty(infoModalPerguntas)) {
                partNumbers.push(infoModalPerguntas);
                partNumbersEstabelecimentos.push(infoModalPerguntasEstabelecimento);
            }

            if (!_.isEmpty(partNumbers)) {

                const {data} = await self.$http.post('pr/perguntas/getPartnumbers', {
                    searchArray: partNumbers,
                    searchArrayEstabelecimentos: partNumbersEstabelecimentos                     
                });
    
                self.partnumbers = data.data.map((item) => {
                    return {
                        key: item.part_number,
                        label: item.part_number,
                        owner_item: item.cod_owner,
                        item: item
                    }
                });
    
                self.selectedPartnumbers = self.partnumbers;
    
                self.buscarResponsavel();
                self.buscarOwner();
            }
            
            // self.buscarOwnerOfUserLogged();
        })
        this.carregando_Responsavel = true;
        this.carregando_Owner = true;
        this.permissaoGrupoPerguntas();

        // Atualizar se a empresa tem o campo owner habilitado
        this.checkHasOwner();
    },
    components: {
        vSelect, ModalListagemPerguntas, Loading, ModalItemInf
    },
    watch: {
        selectedOwners(newValue, oldValue) {
        },
        userType(newValue, oldValue) {
            if (newValue != oldValue) {
                this.selectedOwners = [];
                this.selectedResponsaveis = '';
            }
        },
        selectedPartnumbers(newValue, oldValue) {
            const hasItemWithoutOwner = newValue.some(item => !item.hasOwnProperty('owner_item') || item.owner_item === '' || item.owner_item === null || item.owner_item === undefined);
            if (hasItemWithoutOwner) {
            this.userType = 'user';
            this.buscarResponsavel();
            }  
        },
    },

}
</script>

<style scoped>
    @import "vue-select/src/scss/vue-select.scss";
    .lista-partnumbers {
        background: #6c6e6d24;
        margin-top: 5px;
        padding-top: 10px;
        padding-bottom: 10px;
    }

    .collapse {
        max-height: 150px;
        overflow-y: auto;
    }
    .modal-lg {
        width: 60vw !important;
    }

    .radio-input {
        margin-right: 10px;
    }
.scrollable-select {
    
    max-height: 200px; /* Defina a altura máxima desejada */
    overflow-y: auto;
}
   
</style>