<template>
    <div class="diana-dropzone-container">
        <!-- Área do Dropzone com conteúdo dinâmico -->
        <div class="diana-dropzone" :class="{
            'diana-dropzone--dragging': isDragging,
            'diana-dropzone--disabled': disabled,
            'diana-dropzone--has-files': files.length > 0
        }" @click="openFileSelector" @drop="handleDrop" @dragover="handleDragOver" @dragenter="handleDragEnter"
            @dragleave="handleDragLeave">
            <!-- Estado vazio: Mensagem de upload -->
            <div v-if="files.length === 0" class="diana-dropzone__empty">
                <i class="fa fa-cloud-upload diana-dropzone__icon" aria-hidden="true"></i>
                <p class="diana-dropzone__text">
                    Clique aqui ou arraste arquivos para fazer upload
                </p>
                <small class="diana-dropzone__hint">
                    PDF, DOCX, JPG, PNG - Máx {{ maxFileSizeMb }}MB por arquivo
                </small>
            </div>

            <!-- Estado com arquivos: Grid interno -->
            <div v-else class="diana-dropzone__files">
                <!-- Cabeçalho com informações -->
                <div class="diana-files-header">
                    <strong>{{ files.length }} arquivo(s)</strong>
                    <span class="diana-total-size">{{ totalSizeFormatted }}</span>
                    <small class="diana-add-more">Clique ou arraste para adicionar mais</small>
                </div>

                <!-- Grid de arquivos -->
                <div class="diana-files-grid">
                    <div v-for="(file, index) in files" :key="`file-${index}`" class="diana-file-card" @click.stop>
                        <!-- Preview do arquivo -->
                        <div class="diana-file-preview">
                            <i :class="getFileIconLarge(file)" class="diana-file-icon-large"></i>
                        </div>

                        <!-- Informações do arquivo -->
                        <div class="diana-file-info">
                            <div class="diana-file-name" :title="file.name">
                                {{ truncateFilename(file.name) }}
                            </div>
                            <div class="diana-file-size">{{ formatFileSize(file.size) }}</div>
                        </div>

                        <!-- Botão de remoção -->
                        <button type="button" class="diana-file-remove-card" @click.stop="removeFile(index)"
                            :disabled="disabled" title="Remover arquivo">
                            <i class="fa fa-trash"></i>
                        </button>
                    </div>

                    <!-- Card para adicionar mais arquivos -->
                    <div class="diana-add-card" @click.stop="openFileSelector">
                        <i class="fa fa-plus diana-add-icon"></i>
                        <span class="diana-add-text">Adicionar</span>
                    </div>
                </div>
            </div>

            <!-- Input file oculto -->
            <input ref="fileInput" type="file" :accept="accepted" multiple :disabled="disabled"
                @change="handleFileSelect" style="display: none" />
        </div>

        <!-- Mensagens de erro -->
        <div v-if="errorMessage" class="alert alert-danger diana-error">
            <i class="fa fa-exclamation-triangle"></i>
            {{ errorMessage }}
        </div>
    </div>
</template>

<script>
export default {
    name: 'DianaDropzone',
    props: {
        maxFileSizeMb: {
            type: Number,
            default: 10
        },
        maxTotalSizeMb: {
            type: Number,
            default: 30
        },
        accepted: {
            type: String,
            default: '.pdf,.docx,.jpg,.jpeg,.png'
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            files: [],
            isDragging: false,
            errorMessage: ''
        }
    },
    computed: {
        totalSize() {
            return this.files.reduce((total, file) => total + file.size, 0);
        },
        totalSizeFormatted() {
            return this.formatFileSize(this.totalSize);
        },
        allowedTypes() {
            return [
                'application/pdf',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'image/jpeg',
                'image/png'
            ];
        }
    },
    methods: {
        openFileSelector() {
            if (!this.disabled) {
                this.$refs.fileInput.click();
            }
        },

        handleFileSelect(event) {
            const selectedFiles = Array.from(event.target.files);
            this.processFiles(selectedFiles);
            // Limpar input para permitir selecionar o mesmo arquivo novamente
            event.target.value = '';
        },

        handleDrop(event) {
            event.preventDefault();
            this.isDragging = false;

            if (this.disabled) return;

            const droppedFiles = Array.from(event.dataTransfer.files);
            this.processFiles(droppedFiles);
        },

        handleDragOver(event) {
            event.preventDefault();
        },

        handleDragEnter(event) {
            event.preventDefault();
            if (!this.disabled) {
                this.isDragging = true;
            }
        },

        handleDragLeave(event) {
            event.preventDefault();
            // Só remove o estado de dragging se sair realmente da área
            if (!event.currentTarget.contains(event.relatedTarget)) {
                this.isDragging = false;
            }
        },

        processFiles(newFiles) {
            this.clearError();

            if (!newFiles.length) return;

            // Validar cada arquivo
            const validFiles = [];

            for (const file of newFiles) {
                const validation = this.validateFile(file);

                if (!validation.valid) {
                    this.showError(validation.error);
                    return;
                }

                // Verificar se já existe
                const exists = this.files.some(f => f.name === file.name && f.size === file.size);
                if (!exists) {
                    validFiles.push(file);
                }
            }

            if (validFiles.length === 0) {
                this.showError('Nenhum arquivo novo para adicionar');
                return;
            }

            // Verificar tamanho total
            const newTotalSize = this.totalSize + validFiles.reduce((sum, f) => sum + f.size, 0);
            const maxTotalBytes = this.maxTotalSizeMb * 1024 * 1024;

            if (newTotalSize > maxTotalBytes) {
                this.showError(`Tamanho total excederia ${this.maxTotalSizeMb}MB. Remove alguns arquivos primeiro.`);
                return;
            }

            // Adicionar arquivos válidos
            this.files.push(...validFiles);
            this.emitChange();
        },

        validateFile(file) {
            // Validar tipo
            if (!this.allowedTypes.includes(file.type)) {
                const extension = file.name.split('.').pop().toLowerCase();
                const allowedExts = ['pdf', 'docx', 'jpg', 'jpeg', 'png'];

                if (!allowedExts.includes(extension)) {
                    return {
                        valid: false,
                        error: `Tipo de arquivo não suportado: ${file.name}. Use PDF, DOCX, JPG ou PNG.`
                    };
                }
            }

            // Validar tamanho
            const maxSizeBytes = this.maxFileSizeMb * 1024 * 1024;
            if (file.size > maxSizeBytes) {
                return {
                    valid: false,
                    error: `Arquivo muito grande: ${file.name}. Máximo ${this.maxFileSizeMb}MB.`
                };
            }

            return { valid: true };
        },

        removeFile(index) {
            this.files.splice(index, 1);
            this.clearError();
            this.emitChange();
        },

        clearFiles() {
            this.files = [];
            this.clearError();
            this.emitChange();
        },

        emitChange() {
            this.$emit('change', [...this.files]);
        },

        showError(message) {
            this.errorMessage = message;
            setTimeout(() => {
                this.clearError();
            }, 5000);
        },

        clearError() {
            this.errorMessage = '';
        },

        formatFileSize(bytes) {
            if (bytes === 0) return '0 B';

            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
        },

        truncateFilename(filename) {
            if (filename.length <= 20) return filename;

            const extension = filename.split('.').pop();
            const nameWithoutExt = filename.replace(`.${extension}`, '');

            return nameWithoutExt.substring(0, 15) + '...' + extension;
        },

        getFileIcon(file) {
            const extension = file.name.split('.').pop().toLowerCase();

            switch (extension) {
                case 'pdf':
                    return 'fa fa-file-pdf-o text-danger';
                case 'docx':
                case 'doc':
                    return 'fa fa-file-word-o text-primary';
                case 'jpg':
                case 'jpeg':
                case 'png':
                    return 'fa fa-file-image-o text-success';
                default:
                    return 'fa fa-file-o text-muted';
            }
        },

        getFileIconLarge(file) {
            const extension = file.name.split('.').pop().toLowerCase();

            switch (extension) {
                case 'pdf':
                    return 'fa fa-file-pdf-o';
                case 'docx':
                case 'doc':
                    return 'fa fa-file-word-o';
                case 'jpg':
                case 'jpeg':
                case 'png':
                    return 'fa fa-file-image-o';
                default:
                    return 'fa fa-file-o';
            }
        }
    },

    // Limpar arquivos quando componente for destruído
    beforeDestroy() {
        this.files = [];
    }
}
</script>

<style scoped>
.diana-dropzone-container {
    margin-bottom: 15px;
}

.diana-dropzone {
  border: none; /* Remove a borda padrão */
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
  min-height: 100px;
  position: relative;
  
  /* Cria o efeito de borda pontilhada com espaçamento customizado */
  background-image: 
    repeating-linear-gradient(0deg, #828990 0, #828990 12px, transparent 12px, transparent 24px),
    repeating-linear-gradient(90deg, #828990 0, #828990 12px, transparent 12px, transparent 24px),
    repeating-linear-gradient(180deg, #828990 0, #828990 12px, transparent 12px, transparent 24px),
    repeating-linear-gradient(270deg, #828990 0, #828990 12px, transparent 12px, transparent 24px);
  background-size: 2px 100%, 100% 2px, 2px 100%, 100% 2px;
  background-position: 0 0, 0 0, 100% 0, 0 100%;
  background-repeat: no-repeat;
  
  /* Importante: adicionar o background-color por cima */
  background-origin: border-box;
  background-clip: padding-box, padding-box, padding-box, padding-box, border-box;
}

.diana-dropzone:hover {
    border-color: #0056b3;
    background-color: #e3f2fd;
}

.diana-dropzone--dragging {
    border-color: #28a745;
    background-color: #d4edda;
    border-style: solid;
}

.diana-dropzone--disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background-color: #e9ecef;
}

.diana-dropzone--disabled:hover {
    border-color: #007bff;
    background-color: #e9ecef;
}

.diana-dropzone--has-files {
    padding: 15px;
    min-height: 140px;
}

/* Estado vazio */
.diana-dropzone__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    pointer-events: none;
}

.diana-dropzone__icon {
    font-size: 32px;
    color: #007bff;
    margin-bottom: 10px;
    display: block;
}

.diana-dropzone__text {
    margin: 10px 0 5px 0;
    font-size: 18px;
    color: #495057;
    font-weight: 500;
}

.diana-dropzone__hint {
    color: #6c757d;
    font-size: 12px;
}

/* Estado com arquivos */
.diana-dropzone__files {
    width: 100%;
}

.diana-files-header {
    text-align: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.diana-files-header strong {
    color: #343a40;
    font-size: 13px;
}

.diana-total-size {
    color: #6c757d;
    font-size: 12px;
    margin-left: 8px;
}

.diana-add-more {
    display: block;
    color: #6c757d;
    font-size: 11px;
    margin-top: 3px;
}

/* Grid de arquivos */
.diana-files-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
}

.diana-file-card {
    position: relative;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px 8px;
    text-align: center;
    transition: all 0.2s ease;
    cursor: default;
    min-height: 90px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.diana-file-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
}

.diana-file-card:hover .diana-file-remove-card {
    opacity: 1;
}

.diana-file-preview {
    margin-bottom: 8px;
}

.diana-file-icon-large {
    font-size: 24px;
    color: #6c757d;
}

.diana-file-info {
    flex-grow: 1;
}

.diana-file-name {
    font-size: 11px;
    color: #495057;
    font-weight: 500;
    line-height: 1.2;
    margin-bottom: 4px;
    word-break: break-word;
}

.diana-file-size {
    font-size: 10px;
    color: #6c757d;
}

.diana-file-remove-card {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: none;
    background-color: #dc3545;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
    cursor: pointer;
}

.diana-file-remove-card:hover {
    background-color: #c82333;
}

.diana-file-remove-card i {
    font-size: 10px;
}

/* Card para adicionar mais */
.diana-add-card {
    border: 2px dashed #007bff;
    border-radius: 6px;
    background-color: rgba(0, 123, 255, 0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 90px;
}

.diana-add-card:hover {
    background-color: rgba(0, 123, 255, 0.1);
    border-color: #0056b3;
}

.diana-add-icon {
    font-size: 20px;
    color: #007bff;
    margin-bottom: 4px;
}

.diana-add-text {
    font-size: 11px;
    color: #007bff;
    font-weight: 500;
}

/* Mensagens de erro */
.diana-error {
    margin-top: 10px;
    margin-bottom: 0;
    font-size: 12px;
    padding: 8px 12px;
}

.diana-error i {
    margin-right: 5px;
}

/* Responsividade */
@media (max-width: 768px) {
    .diana-dropzone {
        padding: 15px;
        min-height: 80px;
    }

    .diana-dropzone--has-files {
        padding: 10px;
    }

    .diana-files-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
    }

    .diana-file-card {
        min-height: 80px;
        padding: 8px 6px;
    }

    .diana-dropzone__icon {
        font-size: 24px;
    }

    .diana-dropzone__text {
        font-size: 13px;
    }
}
</style>