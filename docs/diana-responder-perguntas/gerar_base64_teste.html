<!DOCTYPE html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Gerador Base64 para Teste DIANA</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }
      .upload-area {
        border: 2px dashed #007bff;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        margin: 20px 0;
        background-color: #f8f9fa;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      .upload-area:hover {
        background-color: #e3f2fd;
        border-color: #0056b3;
      }
      .upload-area.dragging {
        background-color: #e3f2fd;
        border-color: #0056b3;
      }
      input[type="file"] {
        display: none;
      }
      .file-info {
        margin: 15px 0;
        padding: 15px;
        background-color: #e9ecef;
        border-radius: 5px;
        display: none;
      }
      .result-area {
        margin: 20px 0;
      }
      textarea {
        width: 100%;
        height: 200px;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        font-family: monospace;
        font-size: 12px;
        resize: vertical;
      }
      button {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
        font-size: 14px;
      }
      button:hover {
        background-color: #0056b3;
      }
      button:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }
      .templates {
        margin: 30px 0;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 5px;
      }
      .templates h3 {
        margin-top: 0;
        color: #495057;
      }
      .template-item {
        margin: 10px 0;
        padding: 10px;
        background: white;
        border-radius: 3px;
        border-left: 4px solid #007bff;
      }
      .success {
        color: #28a745;
        font-weight: bold;
      }
      .error {
        color: #dc3545;
        font-weight: bold;
      }
      .warning {
        color: #ffc107;
        background-color: #fff3cd;
        padding: 10px;
        border-radius: 5px;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🧪 Gerador Base64 para Teste DIANA</h1>

      <div class="warning">
        <strong>⚠️ Importante:</strong> Este arquivo é apenas para
        desenvolvimento/teste. Não use em produção e não envie dados sensíveis.
      </div>

      <div
        class="upload-area"
        id="uploadArea"
        onclick="document.getElementById('fileInput').click()"
      >
        <p><strong>📁 Clique aqui ou arraste arquivos</strong></p>
        <p>Tipos aceitos: PDF, DOCX, JPG, JPEG, PNG</p>
        <p>Máximo: 10MB por arquivo</p>
        <input
          type="file"
          id="fileInput"
          accept=".pdf,.docx,.jpg,.jpeg,.png"
          multiple
        />
      </div>

      <div class="file-info" id="fileInfo">
        <h4>📋 Arquivo Selecionado:</h4>
        <div id="fileDetails"></div>
      </div>

      <div class="result-area">
        <h3>📤 Resultado Base64:</h3>
        <textarea
          id="base64Result"
          placeholder="O conteúdo base64 aparecerá aqui..."
        ></textarea>
        <div>
          <button id="copyBtn" onclick="copyToClipboard()" disabled>
            📋 Copiar Base64
          </button>
          <button onclick="generatePayload()" disabled id="payloadBtn">
            📝 Gerar Payload Teste
          </button>
          <button onclick="clearAll()">🗑️ Limpar</button>
        </div>
      </div>

      <div class="templates">
        <h3>🎯 Templates Prontos</h3>

        <div class="template-item">
          <strong>Payload Mínimo (PNG 1x1):</strong>
          <button onclick="loadMinimalTemplate()">📋 Copiar</button>
        </div>

        <div class="template-item">
          <strong>Payload Completo (3 perguntas típicas):</strong>
          <button onclick="loadCompleteTemplate()">📋 Copiar</button>
        </div>

        <div class="template-item">
          <strong>Múltiplos Documentos:</strong>
          <button onclick="loadMultipleDocsTemplate()">📋 Copiar</button>
        </div>
      </div>

      <div id="status"></div>
    </div>

    <script>
      const fileInput = document.getElementById("fileInput");
      const uploadArea = document.getElementById("uploadArea");
      const fileInfo = document.getElementById("fileInfo");
      const fileDetails = document.getElementById("fileDetails");
      const base64Result = document.getElementById("base64Result");
      const copyBtn = document.getElementById("copyBtn");
      const payloadBtn = document.getElementById("payloadBtn");
      const status = document.getElementById("status");

      let currentFile = null;
      let currentBase64 = null;

      // Drag and drop events
      uploadArea.addEventListener("dragover", (e) => {
        e.preventDefault();
        uploadArea.classList.add("dragging");
      });

      uploadArea.addEventListener("dragleave", () => {
        uploadArea.classList.remove("dragging");
      });

      uploadArea.addEventListener("drop", (e) => {
        e.preventDefault();
        uploadArea.classList.remove("dragging");
        const files = e.dataTransfer.files;
        if (files.length > 0) {
          handleFile(files[0]);
        }
      });

      fileInput.addEventListener("change", (e) => {
        if (e.target.files.length > 0) {
          handleFile(e.target.files[0]);
        }
      });

      function handleFile(file) {
        // Validar tipo
        const allowedTypes = [
          "application/pdf",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "image/jpeg",
          "image/png",
        ];
        if (!allowedTypes.includes(file.type)) {
          showStatus(
            "Tipo de arquivo não suportado. Use PDF, DOCX, JPG ou PNG.",
            "error"
          );
          return;
        }

        // Validar tamanho (10MB)
        if (file.size > 10 * 1024 * 1024) {
          showStatus("Arquivo muito grande. Máximo 10MB.", "error");
          return;
        }

        currentFile = file;

        // Mostrar info do arquivo
        fileDetails.innerHTML = `
                <strong>Nome:</strong> ${file.name}<br>
                <strong>Tipo:</strong> ${file.type}<br>
                <strong>Tamanho:</strong> ${(file.size / 1024).toFixed(2)} KB
            `;
        fileInfo.style.display = "block";

        // Converter para base64
        const reader = new FileReader();
        reader.onload = function (e) {
          currentBase64 = e.target.result.split(",")[1]; // Remove data:type;base64,
          base64Result.value = currentBase64;
          copyBtn.disabled = false;
          payloadBtn.disabled = false;
          showStatus("✅ Arquivo convertido com sucesso!", "success");
        };

        reader.onerror = function () {
          showStatus("❌ Erro ao ler arquivo.", "error");
        };

        reader.readAsDataURL(file);
      }

      function copyToClipboard() {
        if (currentBase64) {
          navigator.clipboard
            .writeText(currentBase64)
            .then(() => {
              showStatus("✅ Base64 copiado para clipboard!", "success");
            })
            .catch(() => {
              // Fallback
              base64Result.select();
              document.execCommand("copy");
              showStatus("✅ Base64 copiado!", "success");
            });
        }
      }

      function generatePayload() {
        if (!currentFile || !currentBase64) {
          showStatus("❌ Primeiro selecione um arquivo.", "error");
          return;
        }

        const payload = {
          documentos_upload: [
            {
              nome: currentFile.name,
              conteudo_base64: currentBase64,
            },
          ],
          perguntas: [
            {
              id: 1,
              pergunta: "Qual é o material constitutivo do item?",
            },
            {
              id: 2,
              pergunta: "Qual a aplicação do item (onde é utilizado)?",
            },
            {
              id: 3,
              pergunta: "Qual a função do item (para que serve)?",
            },
          ],
          contexto: {
            cliente_segmento: "Industrial",
            produto_descricao: "Produto de teste para validação",
            ncm_atual: "73269090",
            ncm_fornecedor: "84212900",
          },
        };

        const payloadJson = JSON.stringify(payload, null, 2);
        base64Result.value = payloadJson;

        navigator.clipboard
          .writeText(payloadJson)
          .then(() => {
            showStatus("✅ Payload JSON copiado para clipboard!", "success");
          })
          .catch(() => {
            showStatus(
              "✅ Payload gerado! Copie manualmente do campo de texto.",
              "success"
            );
          });
      }

      function clearAll() {
        currentFile = null;
        currentBase64 = null;
        fileInfo.style.display = "none";
        base64Result.value = "";
        copyBtn.disabled = true;
        payloadBtn.disabled = true;
        fileInput.value = "";
        showStatus("🗑️ Dados limpos.", "success");
      }

      function loadMinimalTemplate() {
        const template = {
          documentos_upload: [
            {
              nome: "teste.png",
              conteudo_base64:
                "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII=",
            },
          ],
          perguntas: [
            {
              id: 1,
              pergunta: "Teste básico?",
            },
          ],
        };

        copyTemplate(template, "Template mínimo");
      }

      function loadCompleteTemplate() {
        const template = {
          documentos_upload: [
            {
              nome: "especificacao.png",
              conteudo_base64:
                "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII=",
            },
          ],
          perguntas: [
            {
              id: 1,
              pergunta: "Qual é o material constitutivo do item?",
            },
            {
              id: 2,
              pergunta: "Qual a aplicação do item (onde é utilizado)?",
            },
            {
              id: 3,
              pergunta: "Qual a função do item (para que serve)?",
            },
          ],
          contexto: {
            cliente_segmento: "Industrial",
            produto_descricao: "Produto de teste para validação da API DIANA",
            ncm_atual: "73269090",
            ncm_fornecedor: "84212900",
          },
        };

        copyTemplate(template, "Template completo");
      }

      function loadMultipleDocsTemplate() {
        const template = {
          documentos_upload: [
            {
              nome: "especificacao.png",
              conteudo_base64:
                "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII=",
            },
            {
              nome: "foto_produto.png",
              conteudo_base64:
                "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYGAAAAAcAAJF2p3BAAAAASUVORK5CYII=",
            },
          ],
          perguntas: [
            {
              id: 1,
              pergunta: "Qual é o material constitutivo do item?",
            },
            {
              id: 2,
              pergunta: "Qual a aplicação do item (onde é utilizado)?",
            },
          ],
          contexto: {
            cliente_segmento: "Automotivo",
            produto_descricao: "Componente automotivo com múltiplos documentos",
            ncm_atual: "87089990",
            ncm_fornecedor: "87089999",
          },
        };

        copyTemplate(template, "Template múltiplos documentos");
      }

      function copyTemplate(template, name) {
        const json = JSON.stringify(template, null, 2);
        base64Result.value = json;

        navigator.clipboard
          .writeText(json)
          .then(() => {
            showStatus(`✅ ${name} copiado para clipboard!`, "success");
          })
          .catch(() => {
            showStatus(`✅ ${name} carregado! Copie manualmente.`, "success");
          });
      }

      function showStatus(message, type) {
        status.innerHTML = `<div class="${type}">${message}</div>`;
        setTimeout(() => {
          status.innerHTML = "";
        }, 5000);
      }

      // Mostrar instruções iniciais
      showStatus(
        "📋 Selecione um arquivo ou use um dos templates prontos para começar.",
        "success"
      );
    </script>
  </body>
</html>
