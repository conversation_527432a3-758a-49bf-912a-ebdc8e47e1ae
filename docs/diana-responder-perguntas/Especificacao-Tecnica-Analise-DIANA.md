# Especificação Técnica — DIANA auxilia cliente a responder perguntas (Responder pendências)

Esta especificação descreve, de forma objetiva e detalhada, como implementar a nova funcionalidade de análise automática de documentos pela DIANA na tela "Responder pendências" do módulo de Perguntas & Respostas.

A implementação deve respeitar o stack já utilizado no projeto:

- Front-end: Vue 2 (Parcel bundler), Bootstrap, vue-select, lodash e o ecossistema já existente em `assets/vuejs`.
- Back-end: CodeIgniter (PHP), controladores sob `application/controllers`, modelos em `application/models`, utilização de libs nativas (cURL) e padrões de configuração existentes (application/config/config.php).
- Armazenamento de arquivos atual: `assets/perguntas/` (arquivos ligados à pergunta) e `assets/respostas/` (arquivos associados a respostas), conforme encontrado no repositório.


## 1. Contexto e referências

- Controller da página: `application/controllers/controle_pendencias.php` (método `responder()`)
- View: `application/views/controle_pendencias/responder.php`
- Componente raiz da tela: `assets/vuejs/src/components/PerguntasRespostas/Resposta/Default.vue`
- Item de pergunta/resposta: `assets/vuejs/src/components/PerguntasRespostas/Resposta/Item.vue`
- Pastas de arquivos existentes: `assets/perguntas/` e `assets/respostas/`
- Parâmetros/funções adicionais por empresa já existentes (ex.: `owner`, `exportar_diana`): uso via `$empresa->funcoes_adicionais`/`$empresa->campos_adicionais`

Entradas de UX: ver os dois prints do Figma em `docs/diana-responder-perguntas/`.

Endpoint externo DIANA fornecido: `http://*************/gt/perguntas`.

Exemplo de body esperado (mínimo obrigatório: id em cada pergunta):

```json
{
  "documentos_upload": [
    { "nome": "string", "conteudo_base64": "string" }
  ],
  "perguntas": [
    { "id": 0, "pergunta": "string" }
  ],
  "contexto": {
    "cliente_segmento": "string",
    "produto_descricao": "string",
    "ncm_atual": "string",
    "ncm_fornecedor": "string"
  }
}
```


## 2. Objetivo e escopo

- Disponibilizar um componente de drag & drop (Dropzone) para upload de documentos (PDF, DOCX, JPG, JPEG, PNG) na tela de resposta de pendências.
- Botão "Análise do DIANA" que envia os documentos e o conjunto de perguntas visíveis para a DIANA.
- Receber as sugestões da DIANA, preencher automaticamente as respostas quando houver alta confiança e exibir um tooltip com referência (página/trecho) e links para os arquivos relacionados (conforme print do Figma).
- Ao salvar as respostas (ação já existente), anexar os arquivos em banco, relacionando-os com as perguntas respondidas.
- Disponibilizar o recurso somente quando habilitado nas "Funções Adicionais" da empresa (feature flag por cliente).

Fora de escopo nesta fase:

- Persistir o resultado completo da análise para reuso futuro (além de logs). Poderá ser feito em fase 2.
- Edição do conteúdo retornado pela DIANA além do que já existe (textarea).


## 3. Requisitos funcionais (RF)

- RF1 — Dropzone Vue 2:
  - Aceitar múltiplos arquivos com tipos: pdf, docx, jpg, jpeg, png.
  - Exibir área tracejada e ícone conforme Figma; permitir clique para selecionar e arrastar & soltar.
  - Mostrar lista/chips de arquivos selecionados com opção de remover.
  - Limites: tamanho máx. arquivo 10 MB, total 30 MB (configurável).

- RF2 — Botão "Análise do DIANA":
  - Habilitado apenas se a função adicional `analise_diana` estiver ativa para a empresa.
  - Ao clicar: validar pelo menos 1 arquivo selecionado (bloquear envio sem arquivo, com mensagem amigável). A validação pode ser tornada flexível por parâmetro futuro; nesta versão, é obrigatória.
  - Desabilitar drop/botão durante a requisição e exibir loading overlay já utilizado no projeto (`vue-loading-overlay`).

- RF3 — Payload para DIANA:
  - `documentos_upload`: gerar `conteudo_base64` em client-side (FileReader) e enviar para o backend interno.
  - `perguntas`: coletar do array `perguntas` já renderizado em `Default.vue`. Cada item deve enviar `{ id, pergunta }`. O id deve considerar as variações retornadas hoje (`id` ou `id_pergunta`).
  - `contexto`: opcional. Caso disponível no backend (por PN/empresa), incluir; caso contrário, enviar campos vazios.

- RF4 — Integração backend interno:
  - Criar endpoint interno (CodeIgniter) `POST pr/diana/analisar` para receber o JSON do front, transformar e chamar a DIANA (endpoint externo).
  - Tratar timeouts, erros HTTP, e retornar um contrato padronizado ao front, independente do formato exato da DIANA (ver seção 6).
  - Logar as requisições e respostas (com truncamento/mascara de base64) em tabela própria.

- RF5 — Exibição dos resultados:
  - Preencher `item.resposta` com a sugestão quando houver e marcar a pergunta como não pendente (`pendente = 0`).
  - Exibir, abaixo da pergunta (antes do textarea), um bloco "Arquivo:" com links clicáveis para os arquivos usados pela sugestão (estilo do Figma). O link deve baixar/abrir a versão enviada pelo usuário (não o arquivo remoto) — ver decisão em 6.4.
  - Exibir o tooltip com: Página, Trecho e os arquivos relacionados. Ajustar o ícone de info já existente em `Item.vue` quando associado à DIANA.

- RF6 — Salvamento dos anexos:
  - Ao clicar no botão "Salvar" já existente, os arquivos carregados na Dropzone devem ser anexados às perguntas que tiverem resposta preenchida (regra atual: relacionar arquivos às perguntas que foram respondidas nesta operação). O caminho e lógica seguem o fluxo atual de `pr/respostas/responderPerguntas`.

- RF7 — Permissões e feature flag:
  - Exibir a Dropzone e o botão apenas se `analise_diana` estiver presente em `$empresa->funcoes_adicionais`.
  - Backend deve validar a permissão e retornar 403 caso o recurso esteja desabilitado para a empresa.


## 4. Requisitos não funcionais (RNF)

- RNF1 — Segurança: validar tipo/tamanho de arquivo tanto no front quanto no back; nunca gravar os arquivos da análise em diretórios web antes do salvamento final. Enviar para a DIANA somente via backend, evitando expor IPs/URLs internos no front.
- RNF2 — Desempenho: limitar a quantidade/tamanho de arquivos; timeout configurável da DIANA (ex.: 30s). Exibir feedback de progresso/estado ao usuário.
- RNF3 — Observabilidade: logs de requisição/resposta (metadados) e códigos de erro padronizados; correlação por usuário/empresa.
- RNF4 — UX: estados desabilitado/carregando, mensagens claras de erro (sem jargões técnicos), manutenção do conteúdo do textarea para edição manual.


## 5. Arquitetura proposta

Fluxo de alto nível:

1) Usuário seleciona arquivos no Dropzone e clica em "Análise do DIANA".
2) Front monta JSON com arquivos base64 + perguntas visíveis.
3) Front envia JSON para `POST pr/diana/analisar` (interno).
4) Backend valida, registra log, chama `http://*************/gt/perguntas`.
5) Backend padroniza a resposta e devolve ao front.
6) Front aplica as sugestões: preenche respostas, exibe tooltip e links de arquivos.
7) Usuário revisa/edita e clica em "Salvar" (fluxo atual), que anexa os arquivos às perguntas respondidas.

Componentes novos/alterados:

- Front:
  - Novo componente `DianaDropzone.vue` (em `assets/vuejs/src/components/PerguntasRespostas/Resposta/`): encapsula o drag & drop, lista e remoção.
  - Ajustes em `Default.vue`: renderizar a Dropzone + botão; montar payload; realizar chamada ao backend; distribuir sugestões por pergunta; manter integração com `attachments` já existente para o "Salvar".
  - Ajustes em `Item.vue`: exibir tooltip "DIANA" (página/trecho) e bloco "Arquivo:" com links aos documentos relacionados.

- Back:
  - Novo controller: `application/controllers/pr/Diana.php` com método `analisar`.
  - Novo service/helper: `application/services/Diana_service.php` (ou em `libraries/`) para encapsular a chamada HTTP e o mapeamento.
  - Configurações novas em `application/config/config.php`:
    - `diana_enabled` (bool, default false)
    - `diana_base_url` (string, default `http://*************/gt`)
    - `diana_timeout` (int segundos, ex.: 30)
    - `diana_max_file_mb` (int, ex.: 10), `diana_max_total_mb` (int, ex.: 30)
  - Logs: tabela `ctr_diana_logs` para auditoria.
  - Parametrização por empresa: usar `funcoes_adicionais` com a flag `analise_diana`.


## 6. Contratos e mapeamentos

### 6.1. Front → Back (interno)

- Endpoint: `POST pr/diana/analisar`
- Header: `Content-Type: application/json`
- Body (proposto):

```json
{
  "documentos_upload": [
    { "nome": "<string>", "conteudo_base64": "<string base64>" }
  ],
  "perguntas": [
    { "id": <int>, "pergunta": "<string>" }
  ],
  "contexto": {
    "cliente_segmento": "<string|null>",
    "produto_descricao": "<string|null>",
    "ncm_atual": "<string|null>",
    "ncm_fornecedor": "<string|null>"
  }
}
```

- Regras:
  - Validar tamanhos e tipos de arquivos.
  - Validar que há pelo menos 1 arquivo.
  - `id` deve ser sempre preenchido; se `Default.vue` tiver apenas `id_pergunta`, mapear para `id` no front antes de enviar.

### 6.2. Back → DIANA (externo)

- Endpoint: `POST {diana_base_url}/perguntas`
- Body: o mesmo formato fornecido na especificação funcional.
- Timeout: configurável (`diana_timeout`).

### 6.3. Back → Front (resposta padronizada)

- Resposta 200 OK:

```json
{
  "ok": true,
  "sugestoes": [
    {
      "id": <int>,
      "pergunta": "<string>",
      "resposta_sugerida": "<string|null>",
      "confianca": <number|null>,
      "referencias": {
        "pagina": <int|null>,
        "trecho": "<string|null>",
        "arquivos": [ { "nome": "<string>", "page": <int|null> } ]
      }
    }
  ]
}
```

- Erro (4xx/5xx):

```json
{ "ok": false, "codigo": "TIMEOUT|INVALID_PAYLOAD|DIANA_ERROR|DISABLED|NO_FILES", "mensagem": "<string amigável>" }
```

### 6.4. Decisões de UX/Arquitetura

- Links de arquivos exibidos no resultado referem-se aos arquivos enviados pelo usuário nesta análise. Não apontaremos para URLs da DIANA; após o "Salvar", estes arquivos serão mantidos em `assets/respostas/` (ou relacionados conforme regra atual) e os links passam a apontar para o armazenamento padrão.
- Caso o usuário feche a página sem salvar, nenhum arquivo da análise deve ficar persistido no servidor (apenas log de metadados da chamada).


## 7. Modelagem de dados

### 7.1. Nova tabela: `ctr_diana_logs`

- Objetivo: auditoria e suporte a troubleshooting.
- Colunas sugeridas:
  - `id` (PK, auto-increment)
  - `id_empresa` (FK lógica)
  - `id_usuario` (FK lógica)
  - `created_at` (datetime)
  - `status_code` (int)
  - `ok` (tinyint)
  - `request_json` (text) — truncar conteúdo base64 (ex.: manter apenas nomes e tamanhos)
  - `response_json` (longtext)
  - `erro` (varchar 255) — código curto

Índices: `(id_empresa, created_at)`.

### 7.2. Parametrização por empresa

- Usar a coluna já existente `funcoes_adicionais` da empresa para incluir a chave `analise_diana`.
- Ajustar a view `responder.php` para disponibilizar a flag no componente Vue via prop (ou injetar via variável global já usada em `layouts/default.php`).


## 8. Alterações no Front-end (detalhado)

### 8.1. Novo componente `DianaDropzone.vue`

- Props:
  - `maxFileSizeMb` (default: config do back; fallback 10)
  - `maxTotalSizeMb` (default: 30)
  - `accepted` (string: `.pdf,.docx,.jpg,.jpeg,.png`)
  - `disabled` (bool)
- Emits:
  - `change` com array de File
  - `remove` (index)
- Estados:
  - arrastando (dragover)
  - lista de arquivos (nome, tamanho, ícone por tipo)
  - mensagens de erro (tamanho/tipo)

### 8.2. `Default.vue`

- Exibir Dropzone + botão "Análise do DIANA" ao topo, ao lado do seletor de partnumbers.
- Nova data/estado: `dianaFiles: File[]`, `dianaLoading: boolean`, `dianaEnabled: boolean` (via prop do PHP), `dianaResults: Map<idPergunta, sugestao>`.
- Nova action `analisarDiana()`:
  - Monta JSON: converte `dianaFiles` para base64 (FileReader async + Promise.all)
  - Extrai perguntas: `{ id: item.id || item.id_pergunta, pergunta: item.pergunta }`
  - Chama `POST pr/diana/analisar`
  - Distribui `resposta_sugerida` em `this.perguntas` (marcando `pendente = 0` quando preencher) e injeta metadados de referência para o `Item.vue` (ex.: `item.dianaReferencia = {...}` e `item.dianaArquivos = [...]`).
- Manter integração com a rotina atual de `answerQuestions()`/`submit()` para que os arquivos da análise sejam enviados ao salvar. Regra: se a pergunta tiver `resposta` preenchida, relacionar os arquivos enviados na análise.

### 8.3. `Item.vue`

- Receber e exibir metadados da DIANA (opcional): `dianaReferencia` (pagina, trecho) e `dianaArquivos` (lista de arquivos)
- Exibir um tooltip custom (usando Bootstrap tooltip já inicializado) com conteúdo:
  - "Página: X"
  - "Trecho: '...’"
- Exibir abaixo do label o bloco "Arquivo:" com links para os arquivos do array `dianaArquivos` (após salvar, os links apontarão para a pasta persistida, igual aos outros).


## 9. Alterações no Back-end (detalhado)

### 9.1. Controller `pr/Diana.php`

- Método `analisar`:
  - Validar permissão via `funcoes_adicionais` (retornar 403 se desabilitado).
  - Validar payload: lista de arquivos (>=1), tipos e tamanhos.
  - Registrar log inicial (request sem base64 integral, apenas metadados por arquivo: nome, tamanho, mime).
  - Chamar `Diana_service::analisar($payload)` com timeout configurado.
  - Padronizar resposta conforme 6.3 e registrar log final.

### 9.2. Service `Diana_service`

- Responsável por:
  - Montar o body esperado pela DIANA (mantendo o formato original);
  - Fazer a chamada HTTP (cURL) com timeout; cabeçalhos `Content-Type: application/json`;
  - Normalizar a resposta em nossa estrutura interna (`sugestoes[...]`).

### 9.3. Configurações

- `application/config/config.php`:
  - Inserir chaves `diana_enabled`, `diana_base_url`, `diana_timeout`, `diana_max_file_mb`, `diana_max_total_mb`.
- Rotas: adicionar em `application/config/routes.php` a rota `pr/diana/analisar` → `pr/diana/analisar`.

### 9.4. Persistência de anexos

- Nenhuma mudança estrutural necessária: reaproveitar o fluxo atual de `pr/respostas/responderPerguntas` para gravação dos anexos e vínculo às perguntas.
- Apenas garantir que os arquivos selecionados na Dropzone populam `attachments`/`infoArquivos[]` já utilizado na rotina de salvar.


## 10. Validações e regras

- Tipos permitidos: `application/pdf`, `application/vnd.openxmlformats-officedocument.wordprocessingml.document`, `image/jpeg`, `image/png`.
- Tamanho máximo por arquivo: 10 MB (config).
- Tamanho total: 30 MB (config).
- Quantidade máxima: 10 arquivos (config opcional, default 10).
- Timeout para DIANA: 30s (config).
- Retentativas: não previsto nesta fase; em caso de timeout, retornar erro amigável e orientar a reduzir tamanho/quantidade.


## 11. Códigos de erro padronizados

- `DISABLED` — função desabilitada para a empresa.
- `NO_FILES` — nenhum arquivo enviado para análise.
- `INVALID_FILE_TYPE` / `INVALID_FILE_SIZE` / `INVALID_TOTAL_SIZE` — violações de arquivo.
- `INVALID_PAYLOAD` — estrutura inválida (sem perguntas válidas, sem ids).
- `TIMEOUT` — a DIANA não respondeu no tempo configurado.
- `DIANA_ERROR` — erro 4xx/5xx recebido da DIANA.
- `UNKNOWN` — erro inesperado.


## 12. Testes e qualidade

- Unitários (PHP):
  - `Diana_service` — montar payload, tratar timeouts, mapear respostas de exemplo para o contrato interno.
  - Controller `Diana::analisar` — validações de payload/erros e códigos de status.

- Integração (PHP):
  - Mock HTTP da DIANA (ex.: via camada de wrapper, injetando cliente HTTP falso) para simular respostas com/sem sugestão, timeout e 500.

- Front (manual + smoke):
  - Dropzone: seleção, arrasto, remoção, validações de tipo/tamanho.
  - Botão "Análise do DIANA": estados de loading, erro, sucesso.
  - Aplicação das sugestões: preenchimento de respostas, exibição de tooltip, links de arquivos.
  - "Salvar": anexos corretamente persistidos por pergunta respondida.

- Observabilidade:
  - Verificar logs em `ctr_diana_logs` para cada chamada.


## 13. Segurança e privacidade

- Não expor IP/URL da DIANA no front; toda comunicação passa pelo backend.
- Sanitizar nomes de arquivos para exibição; nunca confiar no nome enviado pelo cliente para construir caminhos de arquivo.
- Não persistir base64 dos arquivos em logs (apenas metadados como nome, tamanho, mime, quantidade).


## 14. Rollout e feature flag

- Adicionar a chave `analise_diana` em `funcoes_adicionais` por empresa para habilitar a UI e o endpoint.
- Defaults de config com `diana_enabled = false`; habilitar por ambiente/cliente via INI/ENV ou arquivo de config.


## 15. Riscos e mitigação

- Variabilidade do retorno da DIANA: mitigar com camada de normalização no service e tratamento defensivo (campos opcionais, nullables).
- Tamanho dos arquivos e base64: impor limites e orientar o usuário quando exceder; considerar, futuramente, envio streaming ou compactação.
- UX de múltiplos PNs: as perguntas já chegam agrupadas; aplicar sugestão no item correspondente via `id(_pergunta)`.


## 16. Estimativas de esforço

As estimativas a seguir consideram desenvolvimento, code review e ajustes de testes. Valores em horas.

- Back-end (CodeIgniter) — 20h
  - Controller `pr/Diana::analisar` (validações, logs, resposta): 6h
  - Service HTTP `Diana_service` (payload + normalização + timeout): 6h
  - Configurações, rotas e gates por empresa (`funcoes_adicionais`): 3h
  - Migração tabela `ctr_diana_logs` + DAO simples: 2h
  - Tratamento de erros e cobertura de testes unitários/integr.: 3h

- Front-end (Vue 2) — 33h
  - Componente `DianaDropzone.vue` (drag & drop, validações, UI): 8h
  - Integração em `Default.vue` (payload, chamada, estados): 6h
  - Conversão base64 e gerenciamento de anexos para o fluxo de "Salvar": 3h
  - UX "Análise do DIANA" (loading, mensagens, disable): 4h
  - Ajustes em `Item.vue` (tooltip página/trecho + bloco Arquivo): 6h
  - Testes manuais/smoke + hardening: 5h

- Documentação e DevOps leve — 3h
  - Atualização de docs e parâmetros por ambiente: 1h
  - Scripts/checagens simples (lint/build) e instruções de uso: 2h

- QA/UAT — 4h
  - Roteiros com cenários e validação com usuários-chave.

Subtotal: 60h

Buffer de risco (≈20%): +12h

Estimativa total: 72h (faixa recomendada: 60–80h dependendo de reuso de componentes e variação do retorno da DIANA).


## 17. Critérios de aceite

- Exibir Dropzone e botão "Análise do DIANA" somente quando habilitado por empresa.
- Bloquear envio sem arquivos com mensagem clara.
- Ao enviar, ocorre chamada ao backend interno e a DIANA é acionada; em sucesso, pelo menos uma resposta é sugerida quando pertinente.
- Tooltip com página/trecho e links para arquivos conforme layout do Figma.
- Ao salvar, os anexos são persistidos e relacionados às perguntas respondidas, usando o fluxo já existente.
- Logs disponíveis em banco com status da chamada.


## 18. Próximos passos (futuros)

- Persistir cache das últimas sugestões por PN/pergunta (evitar reenvio de mesmos documentos).
- Métricas de assertividade da DIANA (taxa de aproveitamento por pergunta/cliente).
- Suporte a novos tipos de arquivo (ex.: TIFF) e OCR server-side para PDFs imagem-only.


---

Última atualização: 23/09/2025
