# Guia Completo - Teste com Postman

## 🎯 Preparação do Ambiente

### 1. Configurar Base de Dados

```bash
cd /home/<USER>/Face/Becomex/GESTAO-TARIFARIA
php index.php migration migrate
```

### 2. Configurar Empresa de Teste

1. Acesse: `http://localhost/cadastros/empresa`
2. Editar uma empresa existente
3. Em **Funções Adicionais**, marque: `Responder Perguntas Diana`
4. Salvar

### 3. Login na Aplicação

1. Acesse: `http://localhost/login`
2. Faça login com um usuário da empresa configurada
3. **Deixe o navegador aberto** (Postman usará os cookies)

## 📥 Importar Collection no Postman

### 1. Abrir Postman

- Instalar se necessário: [postman.com](https://www.postman.com/downloads/)

### 2. Importar Collection

1. **Import** → **File**
2. Selecionar: `docs/diana-responder-perguntas/DIANA_Backend_Tests.postman_collection.json`
3. **Import**

### 3. Configurar Variáveis

1. Clicar na collection **"DIANA Backend - Responder Perguntas"**
2. Aba **Variables**
3. Configurar:
   - `base_url`: `http://localhost` (ou sua URL)
   - `large_base64_content`: (vamos configurar depois)

## 🔧 Configurar Autenticação

### Opção 1: Copiar Cookies do Navegador (Recomendado)

1. **No navegador** (com login feito):

   - Pressionar `F12` (DevTools)
   - Aba **Application** → **Cookies** → `http://localhost`
   - Copiar nome e valor do cookie de sessão (ex: `ci_session`)

2. **No Postman**:
   - Em cada requisição, aba **Headers**
   - Adicionar: `Cookie: ci_session=valor_copiado`

### Opção 2: Configurar Cookie Global

1. **Postman** → **Settings** → **Cookies**
2. **Add Cookie**:
   - Domain: `localhost`
   - Path: `/`
   - Name: `ci_session` (ou nome do cookie)
   - Value: `valor_copiado_do_navegador`

## 📄 Preparar Documentos Base64

### Gerar Base64 de Arquivo Real

**Opção 1 - Linux/Terminal:**

```bash
# Para um PDF pequeno
base64 -w 0 arquivo.pdf > base64_output.txt

# Para uma imagem
base64 -w 0 imagem.jpg > base64_output.txt
```

**Opção 2 - Online:**

1. Acesse: [base64encode.org](https://www.base64encode.org/)
2. Upload do arquivo
3. Copie o resultado

**Opção 3 - JavaScript (Console do Navegador):**

```javascript
// Cole este código no console do navegador
function fileToBase64() {
  const input = document.createElement("input");
  input.type = "file";
  input.accept = ".pdf,.docx,.jpg,.png";

  input.onchange = function (event) {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = function (e) {
        const base64 = e.target.result.split(",")[1];
        console.log("Nome:", file.name);
        console.log("Base64:", base64);

        // Copiar para clipboard
        navigator.clipboard.writeText(base64);
        console.log("Base64 copiado para clipboard!");
      };
      reader.readAsDataURL(file);
    }
  };

  input.click();
}

fileToBase64();
```

### Arquivo de Teste Pequeno (PNG 1x1)

```
iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII=
```

## 🧪 Executar Testes

### Teste 1: Status do Serviço

**Request:**

```
GET http://localhost/pr/diana/status
```

**Headers:**

```
Accept: application/json
Cookie: ci_session=seu_cookie_aqui
```

**Resultado esperado:**

```json
{
  "service_enabled": true,
  "has_permission": true,
  "empresa_id": 1,
  "timestamp": "2024-01-XX XX:XX:XX"
}
```

### Teste 2: Análise com Documento Real

**Request:**

```
POST http://localhost/pr/diana/analisar
```

**Headers:**

```
Content-Type: application/json
Accept: application/json
Cookie: ci_session=seu_cookie_aqui
```

**Body (raw JSON):**

```json
{
  "documentos_upload": [
    {
      "nome": "documento_teste.pdf",
      "conteudo_base64": "SEU_BASE64_AQUI"
    }
  ],
  "perguntas": [
    {
      "id": 1,
      "pergunta": "Qual é o material constitutivo do item?"
    },
    {
      "id": 2,
      "pergunta": "Qual a aplicação do item (onde é utilizado)?"
    },
    {
      "id": 3,
      "pergunta": "Qual a função do item (para que serve)?"
    }
  ],
  "contexto": {
    "cliente_segmento": "Industrial",
    "produto_descricao": "Produto para análise de material",
    "ncm_atual": "73269090",
    "ncm_fornecedor": "73269099"
  }
}
```

**Resultado esperado:**

```json
{
  "ok": true,
  "sugestoes": [
    {
      "id": 1,
      "pergunta": "",
      "resposta_sugerida": "Aço inoxidável",
      "confianca": null,
      "referencias": {
        "pagina": 1,
        "trecho": "Material: aço inoxidável...",
        "arquivos": [
          {
            "nome": "documento_teste.pdf",
            "page": 1
          }
        ]
      }
    }
  ]
}
```

### Teste 3: Múltiplos Documentos

**Body (raw JSON):**

```json
{
  "documentos_upload": [
    {
      "nome": "especificacao.pdf",
      "conteudo_base64": "BASE64_PDF_AQUI"
    },
    {
      "nome": "foto_produto.jpg",
      "conteudo_base64": "BASE64_JPG_AQUI"
    }
  ],
  "perguntas": [
    {
      "id": 1,
      "pergunta": "Qual é o material constitutivo?"
    },
    {
      "id": 2,
      "pergunta": "Qual a aplicação do item?"
    }
  ],
  "contexto": {
    "cliente_segmento": "Automotivo",
    "produto_descricao": "Componente automotivo",
    "ncm_atual": "87089990",
    "ncm_fornecedor": "87089990"
  }
}
```

### Teste 4: Validação de Erros

**Sem arquivos:**

```json
{
  "documentos_upload": [],
  "perguntas": [
    {
      "id": 1,
      "pergunta": "Teste"
    }
  ]
}
```

**Resultado esperado:**

```json
{
  "ok": false,
  "codigo": "INVALID_PAYLOAD",
  "mensagem": "É necessário enviar pelo menos um arquivo para análise"
}
```

## 📊 Verificar Logs

### Via Postman - Estatísticas

```
GET http://localhost/pr/diana/estatisticas
```

### Via Banco de Dados

```sql
SELECT * FROM ctr_diana_logs ORDER BY created_at DESC LIMIT 10;
```

## 🔍 Troubleshooting

### Erro 401 - Não autenticado

```
Solução: Verificar cookie de sessão
1. Refazer login no navegador
2. Copiar novo cookie
3. Atualizar no Postman
```

### Erro 403 - Sem permissão

```
Solução: Configurar empresa
1. Ir em Cadastros → Empresa
2. Marcar "Responder Perguntas Diana"
3. Salvar
```

### Erro 400 - Invalid JSON

```
Solução: Verificar formato JSON
1. Usar validator JSON online
2. Verificar aspas duplas
3. Verificar vírgulas
```

### Erro 500 - CodeIgniter 2.x

```
Problema: Métodos is_get(), is_post() não existem no CI 2.x
Solução: Controller atualizado para usar:
- $this->input->server('REQUEST_METHOD')
- file_get_contents('php://input')
```

### Timeout na API DIANA

```
Solução: Verificar conectividade
1. Teste com arquivo menor
2. Verificar se está na rede interna
3. Verificar logs de erro
```

## 📝 Templates de Payload

### Template Mínimo

```json
{
  "documentos_upload": [
    {
      "nome": "teste.png",
      "conteudo_base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII="
    }
  ],
  "perguntas": [
    {
      "id": 1,
      "pergunta": "Teste básico?"
    }
  ]
}
```

### Template Completo

```json
{
  "documentos_upload": [
    {
      "nome": "especificacao_tecnica.pdf",
      "conteudo_base64": "JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZw..."
    },
    {
      "nome": "foto_produto.jpg",
      "conteudo_base64": "/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj..."
    }
  ],
  "perguntas": [
    {
      "id": 1,
      "pergunta": "Qual é o material constitutivo do item?"
    },
    {
      "id": 2,
      "pergunta": "Qual a aplicação do item (onde é utilizado)?"
    },
    {
      "id": 3,
      "pergunta": "Qual a função do item (para que serve)?"
    }
  ],
  "contexto": {
    "cliente_segmento": "Industrial",
    "produto_descricao": "Componente mecânico para equipamento industrial",
    "ncm_atual": "73269090",
    "ncm_fornecedor": "84212900"
  }
}
```

## ✅ Checklist de Teste Completo

- [ ] Migração executada
- [ ] Empresa configurada com permissão
- [ ] Login realizado
- [ ] Cookie configurado no Postman
- [ ] Collection importada
- [ ] Teste 1: Status (200 OK)
- [ ] Teste 2: Análise mínima (200 OK com sugestões)
- [ ] Teste 3: Múltiplos arquivos (200 OK)
- [ ] Teste 4: Validação erro (400 Bad Request)
- [ ] Teste 5: Estatísticas (200 OK)
- [ ] Logs salvos no banco

## 🎯 Resultados Esperados

### Sucesso (200 OK)

- Response time: 3-8 segundos
- Estrutura JSON correta
- Campo `ok: true`
- Array `sugestoes` com dados

### Análise Real da DIANA

- `resposta_sugerida` preenchida ou vazia
- `referencias.pagina` com número da página
- `referencias.arquivos` com nome do arquivo
- `referencias.trecho` com texto extraído

**Pronto para testar! 🚀**
