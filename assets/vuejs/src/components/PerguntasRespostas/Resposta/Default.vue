<template>
    <div>
        <loading :active.sync="isLoading" :is-full-page="fullPage">
        </loading>

        <div class="row">
            <div class="col-md-12">
                <label for="nome" class="d-block">
                    Partnumbers
                </label>
                <v-select v-model="this.haspartnumbers" @input="handleSelect" placeholder="Selecione" :multiple="true"
                    :options="getOptions()">
                    <div slot="no-options" name="no-options">Não encontramos nenhuma opção.</div>
                </v-select>
            </div>
        </div>

        <!-- DIANA Upload e Análise -->
        <div v-if="dianaEnabled" class="row diana-section">
            <div class="col-md-8">
                <DianaDropzone ref="dianaDropzone" :disabled="dianaLoading" :max-file-size-mb="10"
                    :max-total-size-mb="30" @change="onDianaFilesChange" />
            </div>
            <div class="col-md-4">
                <div class="diana-actions">
                    <button type="button" class="btn btn-primary diana-analyze-btn"
                        :disabled="dianaLoading || dianaFiles.length === 0 || perguntas.length === 0"
                        @click="analisarDiana">
                        <i v-if="dianaLoading" class="fa fa-spinner fa-spin"></i>
                        <i v-else class="fa fa-robot"></i>
                        {{ dianaLoading ? 'Analisando...' : 'Análise do DIANA' }}
                    </button>

                    <button v-if="dianaFiles.length > 0" type="button" class="btn btn-outline-secondary diana-clear-btn"
                        @click="limparDropzoneDiana" title="Limpar arquivos DIANA">
                        <i class="fa fa-trash"></i>
                        Limpar
                    </button>

                    <small v-if="dianaFiles.length === 0" class="diana-help text-muted">
                        Selecione arquivos para habilitar a análise
                    </small>
                    <small v-else-if="perguntas.length === 0" class="diana-help text-muted">
                        Selecione partnumbers para habilitar a análise
                    </small>

                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-md-12" v-for="(item, index) in perguntas"
                :key="`item-${item.id || item.id_pergunta}-${dianaUpdateKey}`">
                <Item :permissionDelete="permissionDelete" :estabelecimento="item.estabelecimento" :index="index + 1"
                    :partnumbers="item.part_numbers" :item="perguntas[index]"
                    :resposta="typeof resposta[item.id] === 'undefined' || resposta[item.id] == null ? '' : resposta[item.id]"
                    :label="item.pergunta" :tooltips="tooltips" :base-url="baseUrl" @storeFiles="storeFiles"
                    @removeFiles="removeFiles"></Item>
            </div>
        </div>

        <hr />

        <button @click="submit" :disabled="disableButton" class="btn btn-primary" value="1" name="submit"><i
                class="glyphicon glyphicon-floppy-disk"></i> Salvar</button>
        <a :href="baseUrl + 'controle_pendencias'" class="btn">Cancelar</a>
    </div>
</template>
<script>
import 'vue-loading-overlay/dist/vue-loading.css';
import Loading from 'vue-loading-overlay';
import _ from 'lodash';
import vSelect from 'vue-select';
import Item from './Item.vue';
import DianaDropzone from './DianaDropzone.vue';

export default {
    data() {
        return {
            selectedPartnumbers: [],
            partnumbers: [],
            perguntas: [],
            attachments: [],
            isLoading: false,
            fullPage: true,
            disableButton: true,
            permissionDelete: 0,
            itensMarcados: 0,
            partnumberRecebido: '',
            // DIANA
            dianaFiles: [],
            dianaLoading: false,
            dianaResults: new Map(),
            dianaUpdateKey: 0, // Key para forçar re-render dos Items
            resposta: {} // Inicializar como objeto para mapear ID -> resposta
        }
    },
    props: {
        haspartnumbers: {
            required: false,
            type: Array
        },
        estabelecimento: {
            required: false,
            type: Array
        },
        baseUrl: {
            required: false,
            type: String
        },
        resposta: {
            required: true,
            type: String,
            default: {}
        },
        tooltips: {
            required: false,
            type: Array
        },
        dianaEnabled: {
            required: false,
            type: Boolean,
            default: false
        }
    },
    methods: {
        getRespostas() {
            let partnumber = this.haspartnumbers;
            let estabelecimento = this.estabelecimento;

            this.$http.get('pr/respostas/getRespostas', {
                params: {
                    partnumber,
                    estabelecimento
                }
            }).then((response) => {
                if (response) {
                    this.resposta = response.data;
                } else {
                    this.resposta = null;
                }
            });
        },
        removeFiles(ids) {
            this.attachments = this.attachments.filter((item) => {
                return item.ids != ids
            });
        },
        storeFiles(data) {
            this.attachments = this.attachments.filter((item) => {
                return item.ids != data.ids;
            });

            this.attachments.push(data);
        },
        async handleSelect() {
            this.disableButton = true;

            const { data } = await this.getPerguntas();

            this.disableButton = false;

            this.perguntas = data.data.map((item) => {
                return item;
            })
        },
        getPermissao() {

            return this.$http.get('pr/perguntas/getPermissao');
        },
        getPerguntas() {
            let partnumbers = {
                item: this.partnumberRecebido,
                estabelecimento: this.estabelecimento
            };

            return this.$http.get('pr/perguntas/getPerguntasPendentesAgrupadasPn', {
                params: {
                    partnumbers
                }
            });
        },
        getPartnumbers() {

            return this.$http.get('pr/respostas/getPartnumbersSemRespostas', {
                params: {
                    partnumbers: this.partnumberRecebido,
                    estabelecimento: this.estabelecimento
                }
            });

        },

        // DIANA Methods
        onDianaFilesChange(files) {
            this.dianaFiles = files;

            // Se não há mais arquivos DIANA, limpar attachments DIANA
            if (files.length === 0) {
                this.limparArquivosDiana();
            }
        },

        async analisarDiana() {
            if (this.dianaFiles.length === 0) {
                return this.showDianaError('Selecione pelo menos um arquivo para análise');
            }

            if (this.perguntas.length === 0) {
                return this.showDianaError('Nenhuma pergunta disponível para análise');
            }

            this.dianaLoading = true;
            this.isLoading = true; // Mostrar overlay global enquanto processa/analisar

            try {
                // 1. Converter arquivos para base64
                const documentos = await this.convertFilesToBase64(this.dianaFiles);

                // 2. Extrair perguntas do estado atual
                const perguntas = this.perguntas.map(item => ({
                    id: item.id || item.id_pergunta,
                    pergunta: item.pergunta
                }));

                // 3. Montar payload
                const payload = {
                    documentos_upload: documentos,
                    perguntas: perguntas,
                    partnumbers: this.selectedPartnumbers.map(p => p.key),
                    // Dados adicionais podem ser adicionados aqui
                    // contexto: this.buildContexto()
                };

                // 4. Fazer requisição
                const response = await this.callDianaAPI(payload);

                // 5. Processar resultados
                if (response.data && response.data.ok) {
                    const perguntasPreenchidas = this.processarSugestoes(response.data.sugestoes);
                    // Remover overlay antes de exibir modal
                    this.isLoading = false;
                    if (perguntasPreenchidas > 0) {
                        this.showDianaSuccess(`Análise concluída! ${perguntasPreenchidas} resposta(s) preenchida(s) automaticamente.`);
                    } else {
                        this.showDianaSuccess('Análise concluída! Nenhuma resposta automática encontrada nos documentos.');
                    }
                } else {
                    const errorMsg = (response.data && response.data.mensagem) ? response.data.mensagem : 'Erro na análise';
                    // Remover overlay antes de exibir modal
                    this.isLoading = false;
                    this.showDianaError(errorMsg);
                }

            } catch (error) {
                console.error('Erro na análise DIANA:', error);
                let errorMsg = 'Erro inesperado na análise';

                if (error.response) {
                    if (error.response.data && error.response.data.mensagem) {
                        errorMsg = error.response.data.mensagem;
                    } else if (error.response.status === 403) {
                        errorMsg = 'Funcionalidade não habilitada para sua empresa';
                    } else if (error.response.status === 401) {
                        errorMsg = 'Sessão expirada. Faça login novamente';
                    }
                }

                // Remover overlay antes de exibir modal
                this.isLoading = false;
                this.showDianaError(errorMsg);
            } finally {
                this.dianaLoading = false;
                // Garantia de desligar overlay caso não tenha sido desligado acima
                this.isLoading = false;
            }
        },

        convertFilesToBase64(files) {
            return Promise.all(
                files.map(file => {
                    return new Promise((resolve, reject) => {
                        const reader = new FileReader();
                        reader.onload = () => {
                            resolve({
                                nome: file.name,
                                conteudo_base64: reader.result.split(',')[1] // Remove data:type;base64,
                            });
                        };
                        reader.onerror = reject;
                        reader.readAsDataURL(file);
                    });
                })
            );
        },

        callDianaAPI(payload) {
            return this.$http.post('/pr/diana/analisar', payload);
        },

        buildContexto() {
            // Contexto opcional - pode ser expandido futuramente
            return {
                cliente_segmento: '',
                produto_descricao: '',
                ncm_atual: '',
                ncm_fornecedor: ''
            };
        },

        processarSugestoes(sugestoes) {
            if (!sugestoes || !Array.isArray(sugestoes)) {
                console.error('DIANA: Sugestões inválidas:', sugestoes);
                return;
            }

            let perguntasPreenchidas = 0;

            sugestoes.forEach(sugestao => {

                if (sugestao.id) {
                    // Encontrar pergunta correspondente usando diferentes campos possíveis
                    const pergunta = this.perguntas.find(p => {
                        const perguntaId = p.id || p.id_pergunta || p.ids;
                        return perguntaId == sugestao.id; // Comparação flexível (== ao invés de ===)
                    });

                    if (pergunta) {

                        const perguntaId = pergunta.id || pergunta.id_pergunta || pergunta.ids;

                        // Preencher resposta (mesmo que vazia para mostrar que foi processada)
                        if (sugestao.resposta_sugerida) {
                            console.log(`DIANA DEBUG: Preenchendo resposta para pergunta ${perguntaId}:`, sugestao.resposta_sugerida);

                            // Atualizar o objeto pergunta
                            this.$set(pergunta, 'resposta', sugestao.resposta_sugerida);
                            pergunta.pendente = 0; // Marcar como respondida

                            // Atualizar também o objeto resposta usado no template
                            if (!this.resposta) {
                                this.resposta = {};
                            }
                            this.$set(this.resposta, perguntaId, sugestao.resposta_sugerida);

                            perguntasPreenchidas++;

                            console.log(`DIANA DEBUG: Resposta preenchida. pergunta.resposta =`, pergunta.resposta);
                        }

                        // Adicionar metadados DIANA para exibição (sempre, mesmo se resposta vazia)
                        pergunta.dianaReferencia = sugestao.referencias;
                        pergunta.dianaArquivos = (sugestao.referencias && sugestao.referencias.arquivos) ? sugestao.referencias.arquivos : [];

                        // Forçar reatividade do Vue
                        this.$set(pergunta, 'dianaReferencia', sugestao.referencias);
                        this.$set(pergunta, 'dianaArquivos', pergunta.dianaArquivos);

                        // Armazenar para referência
                        this.dianaResults.set(sugestao.id, sugestao);
                    } else {
                        console.warn('DIANA: Pergunta não encontrada para ID:', sugestao.id);
                        console.log('DIANA: IDs disponíveis:', this.perguntas.map(p => p.id || p.id_pergunta || p.ids));
                    }
                }
            });
            // Integrar arquivos DIANA com sistema de anexos existente
            this.integrarArquivosDiana();

            // Forçar re-render dos componentes Item incrementando a key
            this.dianaUpdateKey++;

            return perguntasPreenchidas;
        },

        integrarArquivosDiana() {
            if (this.dianaFiles.length === 0) return;

            console.log('DIANA DEBUG: dianaFiles =', this.dianaFiles);
            console.log('DIANA DEBUG: dianaResults =', this.dianaResults);
            console.log('DIANA DEBUG: perguntas =', this.perguntas);

            // Debug detalhado para cada pergunta
            this.perguntas.forEach((p, index) => {
                const perguntaId = p.id || p.id_pergunta;
                const perguntaIdNumber = parseInt(perguntaId); // Converter para número
                const temResposta = p.resposta && p.resposta.trim();
                const temDianaResult = this.dianaResults.has(perguntaIdNumber);

                console.log(`DIANA DEBUG: pergunta[${index}] =`, {
                    id: perguntaId,
                    idNumber: perguntaIdNumber,
                    resposta: p.resposta,
                    temResposta: temResposta,
                    temDianaResult: temDianaResult,
                    dianaResult: this.dianaResults.get(perguntaIdNumber)
                });
            });

            // Encontrar perguntas que foram respondidas pela DIANA
            const perguntasRespondidas = this.perguntas.filter(p => {
                const perguntaId = p.id || p.id_pergunta;
                const perguntaIdNumber = parseInt(perguntaId); // Converter para número
                return p.resposta && p.resposta.trim() && this.dianaResults.has(perguntaIdNumber);
            });

            console.log('DIANA DEBUG: perguntasRespondidas =', perguntasRespondidas);

            // Para cada pergunta respondida, adicionar os arquivos DIANA aos attachments
            perguntasRespondidas.forEach(pergunta => {
                const perguntaId = pergunta.id || pergunta.id_pergunta;

                // Remover attachment anterior desta pergunta se existir (incluindo DIANA)
                this.attachments = this.attachments.filter(att => att.ids !== perguntaId);

                // Adicionar arquivos DIANA como attachment (mesmo formato dos arquivos normais)
                const attachmentDiana = {
                    ids: perguntaId,
                    qtdFiles: this.dianaFiles.length,
                    fileNames: this.dianaFiles.map(f => f.name),
                    files: this.dianaFiles,
                    isDianaFiles: true // Flag para identificar arquivos DIANA (opcional, para debug)
                };

                console.log('DIANA DEBUG: adicionando attachment =', attachmentDiana);
                this.attachments.push(attachmentDiana);
            });

            console.log('DIANA DEBUG: attachments final =', this.attachments);
        },

        showDianaError(message) {
            // Usar SweetAlert se disponível, senão alert simples
            if (typeof swal !== 'undefined') {
                swal('Erro!', message, 'error');
            } else {
                alert(`Erro: ${message}`);
            }
        },

        showDianaSuccess(message) {
            // Usar SweetAlert se disponível, senão alert simples
            if (typeof swal !== 'undefined') {
                swal('Sucesso!', message, 'success');
            } else {
                alert(`Sucesso: ${message}`);
            }
        },

        // Método para limpar arquivos DIANA dos attachments (quando necessário)
        limparArquivosDiana() {
            this.attachments = this.attachments.filter(att => !att.isDianaFiles);
        },

        // Método para limpar completamente o dropzone DIANA
        limparDropzoneDiana() {
            if (this.$refs.dianaDropzone) {
                this.$refs.dianaDropzone.clearFiles();
            }
            this.dianaFiles = [];
            this.limparArquivosDiana();
            this.dianaResults.clear();
        },

        getOptions() {
            let xx = [];
            for (let i = 0; i < this.partnumbers.length; i++) {
                xx[i] = this.partnumbers[i].key
            };

            return xx;
        },
        async requestPartnumbers() {
            this.disableButton = true;

            const { data } = await this.getPartnumbers();

            this.disableButton = false;

            this.partnumbers = data.data.map((item) => {
                return {
                    key: item.part_number,
                    label: item.part_number,
                    item: item,
                    estabelecimento: item.estabelecimento
                };
            })
        },
        answerQuestions() {
            this.isLoading = true;
            var self = this;
            let form = new FormData();

            // Debug: Log dos attachments para verificar
            console.log('DIANA DEBUG: attachments =', this.attachments);

            this.attachments.forEach((item) => {
                // Adicionar cada arquivo individualmente
                for (let i = 0; i < item.files.length; i++) {
                    form.append('arquivos[]', item.files[i]);
                }

                // Adicionar metadados dos arquivos
                form.append('infoArquivos[]', JSON.stringify({
                    ids: item.ids,
                    quantidade: item.qtdFiles,
                    nomeArquivos: item.fileNames
                }));
            });

            var perguntas = [];

            this.perguntas.forEach((pergunta) => {
                // pergunta['pergunta'] = pergunta['pergunta'].replace(/[\u00A0-\u9999<>\&]/gim, function (i) {
                //     return '&#' + i.charCodeAt(0) + ';';
                // });


                //var textArea = document.createElement('textarea');
                //textArea.innerText = pergunta['pergunta']; // return textArea.innerHTML;
                // pergunta['pergunta'] = textArea.innerHTML;


                //pergunta['pergunta'] = encodeURIComponent(pergunta['pergunta']);


                pergunta['pergunta'] = pergunta['pergunta'].replace(/./gm, function (s) {
                    return (s.match(/[a-z0-9\s]+/i)) ? s : "&#" + s.charCodeAt(0) + ";";
                });

                perguntas.push(pergunta)
            });



            form.append("perguntas", JSON.stringify(perguntas));

            const config = { headers: { 'Content-Type': 'multipart/form-data' } };

            this.disableButton = true;

            this.$http.post("pr/respostas/responderPerguntas", form, config).then((data) => {
                if (data.data.error) {
                    swal("Oops!", data.data.msg, "error");
                } else {
                    this.attachments = [];
                    this.dianaFiles = [];
                    this.dianaResults.clear();
                    swal("Sucesso!", "As perguntas foram respondidas.", "success");


                    setTimeout(() => {
                        location.href = this.baseUrl + 'controle_pendencias';
                    }, 500);
                }

            }).catch((error) => {
                if (error.response && error.response.status === 400 && error.response.data && error.response.data.msg) {
                    swal("Erro!", error.response.data.msg, "error");
                } else {
                    swal("Erro!", "Ocorreu um erro na requisição.", "error");
                }

            });

        },
        submit() {
            let hasUnansweredQuestions = null;
            let unansweredMessage = null;
            let campoObrigatorio = false;
            var self = this;

            for (let i = 0; i < this.perguntas.length; i++) {
                const item = this.perguntas[i];

                if (_.isEmpty(item.resposta) && !_.isEmpty(this.resposta[item.id])) {
                    item.resposta = this.resposta[item.id];
                }

                if (_.isEmpty(item.resposta) && item.obrigatorio == "1") {
                    swal({
                        title: "Atenção!",
                        text: "Esta é uma pergunta obrigatória para responder <br><strong>" + item.pergunta + "</strong>",
                        type: "warning",
                        confirmButtonText: "OK",
                        showConfirmButton: true,
                        showCancelButton: false,
                    });

                    campoObrigatorio = true;

                    break;
                }

                // if (_.isEmpty(item.resposta) && !item.hasOwnProperty('resposta')) {
                if (item.pendente == 1) {
                    hasUnansweredQuestions++;
                }
            };

            if (campoObrigatorio) {
                return false;
            }

            unansweredMessage = hasUnansweredQuestions == 1 ? "pergunta" : "perguntas";

            if (hasUnansweredQuestions) {
                swal({
                    title: 'Atenção',
                    text: `Você deixou de responder a ${hasUnansweredQuestions} ${unansweredMessage}. Deseja continuar?`,
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Continuar',
                    cancelButtonText: 'Cancelar',
                    confirmButtonClass: 'btn btn-primary btn-margin-left',
                    cancelButtonClass: 'btn',
                    reverseButtons: true,
                    buttonsStyling: false
                }).then(function () {
                    self.isLoading = true;
                    self.answerQuestions();
                }, function (dismiss) {
                    //do nothing
                });
            } else {
                self.isLoading = true;
                self.answerQuestions();
            }
        }
    },
    async mounted() {

        this.isLoading = true;
        this.disableButton = true;
        this.permissionDelete = await this.getPermissao();
        this.partnumberRecebido = this.haspartnumbers;
        await this.requestPartnumbers();

        let itensValue = this.haspartnumbers.map((item) => {
            return item.split('&')[0];
        })

        this.haspartnumbers = itensValue.filter(function (este, i) {
            return itensValue.indexOf(este) === i;
        });

        this.disableButton = false;

        // Inicializar objeto resposta se vier como string da prop
        if (typeof this.resposta === 'string') {
            try {
                this.resposta = this.resposta ? JSON.parse(this.resposta) : {};
            } catch (e) {
                this.resposta = {};
            }
        }

        this.getRespostas();
        if (!_.isEmpty(this.haspartnumbers)) {
            this.selectedPartnumbers = this.partnumbers.filter((item) => {
                let partnumberIncluded = this.haspartnumbers.filter(pn => pn == item.key);
                return !_.isEmpty(partnumberIncluded);
            });

            this.handleSelect();
        }
        this.isLoading = false;
    },
    components: {
        vSelect, Item, Loading, DianaDropzone
    }
}
</script>

<style scoped>
@import "vue-select/src/scss/vue-select.scss";

.mt-5 {
    margin-top: 15px;
}

/* DIANA Styles */
.diana-section {
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 15px;
}

.diana-actions {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    padding-left: 15px;
}

.diana-analyze-btn {
    width: 100%;
    padding: 12px 20px;
    font-weight: 500;
    margin-bottom: 8px;
}

.diana-analyze-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.diana-clear-btn {
    width: 100%;
    padding: 8px 15px;
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 12px;
}

.diana-help {
    display: block;
    text-align: center;
    font-size: 11px;
    line-height: 1.3;
}

/* Responsividade para DIANA */
@media (max-width: 768px) {
    .diana-section {
        padding: 10px;
    }

    .diana-actions {
        margin-top: 15px;
        padding-left: 0;
    }

    .diana-analyze-btn {
        padding: 10px 15px;
    }
}
</style>