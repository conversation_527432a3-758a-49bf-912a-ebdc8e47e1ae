# Instruções para Teste Frontend DIANA

## 🎯 Preparação para Teste

### 1. Compilar Componentes Vue.js

```bash
cd /home/<USER>/Face/Becomex/GESTAO-TARIFARIA

# Instalar dependências (se necessário)
npm install

# Compilar em modo de desenvolvimento (com watch)
npm run watch

# OU compilar uma vez para produção
npm run build
```

### 2. Executar Migração

```bash
php index.php migration migrate
```

### 3. Configurar Empresa

1. Acesse: `http://localhost/cadastros/empresa`
2. Editar uma empresa existente
3. Em **Funções Adicionais**, marque: `✅ Responder Perguntas Diana`
4. **Salvar**

### 4. Verificar Logs de Compilação

Verificar se não há erros de compilação Vue:

- Console do terminal (npm run watch)
- DevTools do navegador (F12 → Console)

## 🧪 Cenários de Teste

### Teste 1: Visualização do Componente

1. **Login** na aplicação
2. **Ir para:** Controle de pendências → Responder pendências
3. **Verificar:**
   - ✅ Dropzone aparece abaixo do seletor de partnumbers
   - ✅ Botão "Análise do DIANA" visível
   - ✅ Layout conforme Figma

**Se DIANA não aparece:**

- Verificar se empresa tem permissão
- Verificar console JavaScript por erros

### Teste 2: Upload de Arquivos

1. **Clique** na área do dropzone
2. **Selecione** arquivo PDF/JPG/PNG
3. **Verificar:**
   - ✅ Arquivo aparece na lista
   - ✅ Ícone correto por tipo
   - ✅ Tamanho formatado
   - ✅ Botão de remoção funciona

**Drag & Drop:**

1. **Arraste** arquivo do explorador
2. **Solte** na área dropzone
3. **Verificar** mesmo comportamento

### Teste 3: Validações de Arquivo

**Arquivo muito grande (>10MB):**

- Tentar upload de arquivo grande
- Deve mostrar erro vermelho

**Tipo não suportado:**

- Tentar upload de .txt ou .zip
- Deve mostrar erro sobre tipo

**Múltiplos arquivos (>30MB total):**

- Adicionar vários arquivos grandes
- Deve mostrar erro de limite total

### Teste 4: Integração com Backend

1. **Selecionar partnumbers** (para ter perguntas)
2. **Adicionar arquivos** no dropzone
3. **Clicar** "Análise do DIANA"
4. **Verificar:**
   - ✅ Botão mostra "Analisando..." com spinner
   - ✅ Após ~5s, retorna resultado ou erro
   - ✅ Respostas preenchidas automaticamente
   - ✅ Referências DIANA aparecem nos Items

### Teste 5: Exibição de Resultados

**Após análise bem-sucedida:**

- ✅ Perguntas respondidas marcadas com ícone robot
- ✅ Tooltip DIANA com página/trecho
- ✅ Bloco "Arquivo:" com links
- ✅ Texto da resposta preenchido

### Teste 6: Salvamento Final

1. **Revisar** respostas preenchidas pela DIANA
2. **Editar** se necessário
3. **Clicar** "Salvar"
4. **Verificar:**
   - ✅ Arquivos anexados às perguntas respondidas
   - ✅ Salvamento normal funciona

## 🔧 Troubleshooting Frontend

### Dropzone não aparece

```
Causa: Permissão não configurada
Solução: Marcar "Responder Perguntas Diana" na empresa
```

### Erro de compilação Vue

```
Causa: Componente não encontrado
Solução: Verificar import em Default.vue
npm run build
```

### Botão DIANA desabilitado

```
Causas possíveis:
- Nenhum arquivo selecionado
- Nenhuma pergunta carregada
- Service em loading
```

### Erro 403 ao analisar

```
Causa: Backend rejeitando permissão
Solução: Verificar funcoes_adicionais da empresa
```

### Erro de CORS/Sessão

```
Causa: Sessão expirada
Solução: Refazer login
```

## 📱 Layout Responsivo

### Desktop (>768px)

- Dropzone: 8 colunas
- Botão: 4 colunas
- Layout horizontal

### Mobile (<768px)

- Dropzone: 12 colunas
- Botão: 12 colunas (abaixo)
- Layout vertical

## 🎨 Elementos Visuais

### Cores

- **Dropzone border:** #007bff (azul Bootstrap)
- **Hover:** #e3f2fd (azul claro)
- **Dragging:** #d4edda (verde claro)
- **Erro:** #f44336 (vermelho)

### Ícones Font Awesome

- **Upload:** `fa-cloud-upload` (32px)
- **Robot DIANA:** `fa-robot` (14px)
- **Arquivos:** `fa-file-pdf-o`, `fa-file-image-o`, etc.
- **Remover:** `fa-times` (pequeno)

### Estados do Botão

- **Normal:** btn-primary azul
- **Loading:** spinner + texto "Analisando..."
- **Disabled:** opacity 0.6 + cursor not-allowed

## 📊 Dados Esperados

### Estrutura da Pergunta (após DIANA)

```javascript
{
  id: 1,
  pergunta: "Qual é o material?",
  resposta: "Aço inoxidável", // Preenchido pela DIANA
  pendente: 0, // Marcado como respondida
  dianaReferencia: {
    pagina: 2,
    trecho: "Material: aço inoxidável...",
    arquivos: [...]
  },
  dianaArquivos: [
    { nome: "spec.pdf", page: 2 }
  ]
}
```

### Fluxo de Attachments

```javascript
// Arquivos DIANA são adicionados ao attachments[] existente
attachments: [
  {
    ids: 1, // ID da pergunta
    qtdFiles: 2,
    fileNames: ["spec.pdf", "foto.jpg"],
    files: [File, File], // Objetos File do dropzone
  },
];
```

## ✅ Checklist Final

**Antes de testar:**

- [ ] Migração executada
- [ ] Empresa configurada
- [ ] Backend testado (Postman)
- [ ] Vue compilado sem erros

**Durante teste:**

- [ ] Dropzone aparece
- [ ] Upload funciona
- [ ] Validações funcionam
- [ ] API conecta
- [ ] Respostas preenchem
- [ ] Salvamento funciona

**Resultado esperado:**

- 🎯 Layout igual ao Figma
- ⚡ Funcionalidade completa
- 🔒 Permissões funcionais
- 💾 Integração com save existente

## 🚀 Próximos Passos

Se tudo funcionar:

1. **Testes de aceitação** com usuários
2. **Ajustes de UX** se necessário
3. **Deploy para produção**

**Frontend 100% implementado! 🎉**
