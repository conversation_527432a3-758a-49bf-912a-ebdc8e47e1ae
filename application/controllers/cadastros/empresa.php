<?php

class Empresa extends MY_Controller
{

    public function __construct()
    {
        parent::__construct();

        $this->load->model('empresa_model');

        if (!is_logged()) {
            redirect('/login');
        }

        if (!has_role('gerenciar_empresas') && !has_role('consultor')) {
            show_permission();
        }

        $this->load->library('breadcrumbs');
    }

    public function index()
    {
        $this->session->set_flashdata('error_franquia', '');
        $this->load->model('segmento_model');
        $query_str = '';
        if (!empty($this->input->get())) {
            $params = $this->input->get();
            unset($params['per_page']);
            $query_str = http_build_query($params);
        }
        $this->load->library('pagination');

        $page = $this->input->get('per_page');
        $limit = 15;

        if ($this->input->is_set('reset_filter')) {
            $this->empresa_model->clear_states();
        } else {
            if ($this->input->is_set('nome_cnpj') && $this->input->get('nome_cnpj') != '') {
                $this->empresa_model->set_state('filter.nome_cnpj', $this->input->get('nome_cnpj'));
            } else {
                $this->empresa_model->unset_state('filter.nome_cnpj');
            }

            if ($this->input->is_set('id_segmento') && $this->input->get('id_segmento') != '') {
                $this->empresa_model->set_state('filter.id_segmento', $this->input->get('id_segmento'));
            } else {
                $this->empresa_model->unset_state('filter.id_segmento');
            }
        }

        $this->empresa_model->set_state('filter.order_by', 'id_empresa DESC');

        $data = array();
        $total_entries = $this->empresa_model->get_total_entries();
        $data['list'] = $this->empresa_model->get_entries($limit, ($page > 0 ? $page - 1 : 0) * $limit);
        $data['segmentos'] = $this->segmento_model->get_entries();

        $config['base_url'] = base_url("cadastros/empresa?{$query_str}");
        $config['use_page_numbers'] = TRUE;
        $config['total_rows'] = $total_entries;
        $config['per_page'] = $limit;
        $config['page_query_string'] = TRUE;

        $this->pagination->initialize($config);

        $data['pagination'] = $this->pagination->create_links();

        $this->title = "Empresas";

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Empresas', '/cadastros/empresa/');

        $this->render('cadastros/empresa/default', $data);
    }

    public function exportar_franquia()
    {

        //  $filename = 'perguntas_pendentes_' . date('Y-m-d_H-i-s') . '.xlsx';

        // header('Content-Type: application/vnd.ms-excel');
        // header('Content-Disposition: attachment;filename="' . $filename . '"');
        // header('Cache-Control: max-age=0');

        $this->load->model('item_model');
        $id_empresa = !empty($this->input->get('id_empresa', TRUE)) ? $this->input->get('id_empresa', TRUE) :  sess_user_company();
        $ano = $this->input->get('ano', TRUE);
        $meses_str = $this->input->get('meses', TRUE);  

        if (empty($id_empresa) || !is_numeric($id_empresa) || empty($ano) || !is_numeric($ano) || empty($meses_str)) {
            show_error('Parâmetros de exportação inválidos ou ausentes.', 400);
            return;
        }
 
        
 
        $meses_array = explode(',', $meses_str);
        $dados = [];
        $itens = [];
        foreach ($meses_array as $mes) {
        
            if (!is_numeric($mes) || $mes < 1 || $mes > 12) {
                continue;  
            }
 
            $data_inicio = sprintf('%04d-%02d-01', $ano, $mes);
 
            try {
                $date_obj = new DateTime($data_inicio);
                $data_fim = $date_obj->format('Y-m-t');  
            } catch (Exception $e) {
                // Trata caso a data seja inválida por algum motivo
                continue; 
            }
 
            $saldo = $this->item_model->get_saldo_mes($data_inicio, $data_fim, $id_empresa);
 
            $dados = $this->item_model->get_part_numbers_franquia($ano, $mes, $data_inicio, $data_fim, $saldo['total_franquia'], $id_empresa);
    
            $itens =  array_merge($itens, $dados);
    
        }
        $this->session->set_flashdata('error_franquia', '');
        
        if (!empty($itens))
        {

            $token = $this->input->get('downloadToken');  

            if ($token) {
                setcookie(
                    'downloadToken',  
                    $token,            
                    0,                 
                    '/'                
                );
            }
            $this->exportar($itens);
        }   
        $this->session->set_flashdata('error_franquia', 'Não possui itens que tenham excedido a franquia');
        redirect("cadastros/empresa/editar/{$id_empresa}");
 
    }
  
    public function exportar($itens)
      {
         $filename = 'relatorio_franquia_' . date('Y-m-d_H-i-s') . '.xlsx';

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        $k = 0;
        $count_block = 1;
        
     

        $this->load->library('Excel');

        $this->excel->setActiveSheetIndex(0);
        
        $this->excel->getActiveSheet()->setTitle('Log - Atribuição de grupos');
        $this->excel->getActiveSheet()->setCellValue('A1', 'Mês de referência');
        $this->excel->getActiveSheet()->setCellValue('B1', 'Ano de referência');
        $this->excel->getActiveSheet()->setCellValue('C1', 'Partnumber');
        $this->excel->getActiveSheet()->setCellValue('D1', 'Data de criação do Partnumber');
        $this->excel->getActiveSheet()->setCellValue('E1', 'NCM');
        $this->excel->getActiveSheet()->setCellValue('F1', 'Data de envio para o cliente');
        $this->excel->getActiveSheet()->setCellValue('G1', 'Motivo');
        $this->excel->getActiveSheet()->setCellValue('H1', 'Prioridade');
        $this->excel->getActiveSheet()->setCellValue('I1', 'Químico');
        $this->excel->getActiveSheet()->setCellValue('J1', 'Custo por item adicional');
        $this->excel->getActiveSheet()->setCellValue('K1', 'Custo por item adicional');
        $this->excel->getActiveSheet()->getStyle('A1:K1')->getFont()->setBold(true);
        $this->excel->getActiveSheet()->getStyle('A1:K1')->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()->setARGB('F9FF00');

        $this->excel->getActiveSheet()->getStyle('A1:I1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        
        $this->excel->getActiveSheet()->getColumnDimension('A')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('B')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('C')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('D')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('E')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('F')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('G')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('H')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('I')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('J')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('K')->setWidth('30');
        if (!empty($itens)) {
            $horizontal_left = array(
                'alignment' => array(
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                ),
            );
            $item_formatado = [];
            $motivo = '';
            foreach ($itens as $i) {

 
                if (!empty($i->qtd_homologados))
                {
                    if ($i->qtd_homologados > 1) {
                        $motivo = 'Homologação';
                    } else {
                        $motivo = 'Re-Homologação';
                    }
                } else {
                    $motivo = ''; 
                }

                $this->excel->getActiveSheet()->setCellValue('A1', 'Mês de referência');	
                $this->excel->getActiveSheet()->setCellValue('B1', 'Ano de referência');
                $this->excel->getActiveSheet()->setCellValue('C1', 'Partnumber');
                $this->excel->getActiveSheet()->setCellValue('D1', 'Data de criação do Partnumber');
                $this->excel->getActiveSheet()->setCellValue('E1', 'NCM');
                $this->excel->getActiveSheet()->setCellValue('F1', 'Data de envio para o cliente');
                $this->excel->getActiveSheet()->setCellValue('G1', 'Motivo');
                $this->excel->getActiveSheet()->setCellValue('H1', 'Prioridade');
                $this->excel->getActiveSheet()->setCellValue('I1', 'Químico');
                $this->excel->getActiveSheet()->setCellValue('J1', 'Custo por item adicional');
                $this->excel->getActiveSheet()->setCellValue('K1', 'Data da homologação');
                $block[] = $count_block;

                $data_criacao = !empty($i->data_criacao) ? new DateTime($i->data_criacao) : '';
                $data_homologado = !empty($i->data_homologado) ? new DateTime($i->data_homologado) : '';
                $data_envio_cliente = !empty($i->data_envio_cliente) ? new DateTime($i->data_envio_cliente) : '';

                $data_criacao = !empty($data_criacao) ? $data_criacao->format('d/m/Y H:i') : '';
                $data_homologado = !empty($data_homologado) ? $data_homologado->format('d/m/Y H:i') : '';
                $data_envio_cliente = !empty($data_envio_cliente) ? $data_envio_cliente->format('d/m/Y H:i') : '';
                $nomeMes = '';
                switch ($i->mes) {
                    case 1:
                        $nomeMes = 'Janeiro';
                        break;
                    case 2:
                        $nomeMes = 'Fevereiro';
                        break;
                    case 3:
                        $nomeMes = 'Março';
                        break;
                    case 4:
                        $nomeMes = 'Abril';
                        break;
                    case 5:
                        $nomeMes = 'Maio';
                        break;
                    case 6:
                        $nomeMes = 'Junho';
                        break;
                    case 7:
                        $nomeMes = 'Julho';
                        break;
                    case 8:
                        $nomeMes = 'Agosto';
                        break;
                    case 9:
                        $nomeMes = 'Setembro';
                        break;
                    case 10:
                        $nomeMes = 'Outubro';
                        break;
                    case 11:
                        $nomeMes = 'Novembro';
                        break;
                    case 12:
                        $nomeMes = 'Dezembro';
                        break;
                    default:
                        $nomeMes = 'Mês inválido';
                        break;
                }
                
     

                $item_formatado[$k] = array(
                    $nomeMes,
                    $i->ano,
                    $i->part_number,
                    $data_criacao,
                    $i->ncm_proposto,
                    $data_envio_cliente,
                    $motivo,
                    $i->prioridade,
                    $i->quimico,
                    number_format($i->custo, 2, ',', '.'),
                    $data_homologado
                );
                $k++;
                $count_block++;
            }


            $colunas = [0 => 'A', 1 => 'B', 2 => 'C', 3 => 'D' , 4 => 'E', 5 => 'F', 6 => 'G', 7 => 'H', 8 => 'I', 9 => 'J' , 10 => 'K'  ];
            $count = 1;
            foreach ($item_formatado as $k => $data_item) {
   
                $count++;
                foreach ($colunas as $y => $coluna){
                    $this->excel->getActiveSheet()->setCellValueExplicit($coluna. $count, $data_item[$y], PHPExcel_Cell_DataType::TYPE_STRING);
                }
            }

 

            $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');
            $objWriter->save('php://output');
        }
    }

    public function excluir()
    {

        $id_list = $this->input->post('id_list');
        $return = array();
        $return['status'] = FALSE;

        if ($this->empresa_model->remove($id_list)) {
            $return['status'] = TRUE;
            $this->message_next_render('Exclusão realizada com sucesso!');
        }

        echo json_encode($return);
    }

    public function novo()
    {
        $this->load->model(array(
            'empresa_model',
            'segmento_model',
            'empresa_curva_attrs_model',
            'pais_model',
            'perfil_model',
            'empresa_pais_model',
            'squad_model'
        ));

        $this->title = "Empresa &gt; Nova";

        $data = array();

        $data['segmentos'] = $this->segmento_model->get_entries();
        $data['usuarios_ativos'] = $this->usuario_model->get_entries_by_role('fiscal', null, false, null, 'ativo');

        $data['paises'] = $this->pais_model->get_entries_out_brasil();
        $data['perfis'] = $this->perfil_model->get_entries();
        $data['squads'] = $this->squad_model->get_entries();

        if ($this->input->post('submit')) {

            $this->load->library('form_validation');

            $this->form_validation->set_rules('razao_social', 'Razão Social', 'trim|required');
            $this->form_validation->set_rules('nome_fantasia', 'Nome Fantasia', 'trim');
            $this->form_validation->set_rules('descricao_max_caracteres', 'Tamanho da descrição proposta resumida', 'trim');
            $this->form_validation->set_rules('cnpj', 'CNPJ', 'trim|required|is_unique[empresa.cnpj]');

            $this->form_validation->set_rules('tolerancia', 'Tolerância', 'trim|callback_check_tolerancia');
            $this->form_validation->set_rules('tipo_tolerancia', 'Tipo Tolerância', 'trim');
            $this->form_validation->set_rules('estabelecimento_default', 'Estabelecimento', 'trim|required');
            $this->form_validation->set_rules('funcoes_adicionais', 'Funções Adicionais', 'required');

            if (in_array("destinatatio_tec", $this->input->post('funcoes_adicionais'))) {
                $this->form_validation->set_rules('destinatarios_tec', 'Destinatarios TEC', 'trim|required|callback_validate_emails');
            } else {
                $this->form_validation->set_rules('destinatarios_tec', 'Destinatarios TEC', 'trim|callback_validate_emails');
            }

            $valid_upload = TRUE;

            if (isset($_FILES['arquivo']) && $_FILES['arquivo']['size'] > 0) {
                $config['upload_path'] = FCPATH . config_item('upload_logo_empresa_path');
                $config['allowed_types'] = 'gif|jpg|png';
                $config['max_width'] = '800';
                $config['max_height'] = '600';
                $config['encrypt_name'] = TRUE;

                $this->load->library('upload');
                $this->upload->initialize($config);

                $valid_upload = $this->upload->do_upload('arquivo');

                if ($valid_upload) {
                    $upload_data  = $this->upload->data();
                    $logo_filename = $upload_data['file_name'];
                }
            }

            if ($this->form_validation->run() == TRUE && (isset($valid_upload) && $valid_upload)) {

                if (!empty($this->input->post('campos_adicionais'))) {
                    $campos_adicionais = implode('|', $this->input->post('campos_adicionais'));
                }

                if (!empty($this->input->post('funcoes_adicionais'))) {
                    $funcoes_adicionais = implode('|', $this->input->post('funcoes_adicionais'));
                } else {
                    $funcoes_adicionais = NULL;
                }
                $primeira_notificacao = !empty($this->input->post('percentual_primeira_notificacao')) 
                ? str_replace('%', '', $this->input->post('percentual_primeira_notificacao')) 
                : null;
            
                $segunda_notificacao = !empty($this->input->post('percentual_segunda_notificacao')) 
                    ? str_replace('%', '', $this->input->post('percentual_segunda_notificacao')) 
                    : null;
                
                $excedente = !empty($this->input->post('percentual_excedente'))            ? str_replace('%', '', $this->input->post('percentual_excedente'))            : null;

                $data = array(
                    'razao_social'              => $this->input->post('razao_social'),
                    'nome_fantasia'             => $this->input->post('nome_fantasia'),
                    'cnpj'                      => $this->input->post('cnpj'),
                    'id_segmento'               => $this->input->post('id_segmento'),
                    'retira_icms_preco'         => (int) $this->input->post('retira_icms_preco'),
                    'retira_pis_preco'          => (int) $this->input->post('retira_pis_preco'),
                    'retira_cofins_preco'       => (int) $this->input->post('retira_cofins_preco'),
                    'tipo_tolerancia'           => $this->input->post('tipo_tolerancia'),
                    'tolerancia'                => $this->input->post('tolerancia'),
                    'descricao_max_caracteres'  => $this->input->post('descricao_max_caracteres'),
                    'updated_at'                => date('Y-m-d H:i:s'),
                    'campos_adicionais'         => $campos_adicionais,
                    'porta_a_porta'             => $this->input->post('venda_porta_a_porta'),
                    'estabelecimento_default'   => $this->input->post('estabelecimento_default'),
                    'recebe_email_pendencias'   => $this->input->post('recebe_email_pendencias'),
                    'id_resp_fiscal'            => $this->input->post('id_resp_fiscal'),
                    'funcoes_adicionais'        => $funcoes_adicionais,
                    'app_a_seguimento'          => $this->input->post('app_a_seguimento'),
                    'app_b_seguimento'          => $this->input->post('app_b_seguimento'),
                    'app_a_cod'                 => $this->input->post('app_a_cod'),
                    'app_b_cod'                 => $this->input->post('app_b_cod'),
                    'id_squad'                  => $this->input->post('id_squad'),
                    'app_a_modelo'              => $this->input->post('app_a_modelo'),
                    'app_b_modelo'              => $this->input->post('app_b_modelo'),
                    'destinatarios_tec'         => $this->input->post('destinatarios_tec'),
                    'destinatarios_revisao_pucomex'         => $this->input->post('destinatarios_revisao_pucomex'),
                    'perfil_usuario_padrao_id'  => !empty($this->input->post('perfil_usuario_padrao_id')) ? $this->input->post('perfil_usuario_padrao_id') : null,

                    'habilitar_uso_franquia' => (int) $this->input->post('habilitar_uso_franquia'), // Checkbox
                    'quantidade_franquia_mensal' => $this->input->post('quantidade_franquia_mensal'),
                    'habilitar_cobrancas_adicionais' => (int) $this->input->post('habilitar_cobrancas_adicionais'), // Checkbox
                    'valor_padrao_default'   => $this->converterDecimaisParaPonto($this->input->post('valor_padrao_default')),
                    'valor_quimico_default'  => $this->converterDecimaisParaPonto($this->input->post('valor_quimico_default')),
                    'habilitar_notificacoes' => (int) $this->input->post('habilitar_notificacoes'), // Checkbox
                    'destinatarios_excedente'     => $this->input->post('destinatarios_excedente'),
                    'percentual_primeira_notificacao'     => $primeira_notificacao,
                    'percentual_segunda_notificacao'     => $segunda_notificacao,
                    'habilitar_bloqueio'     => (int) $this->input->post('habilitar_bloqueio'), // Checkbox
                    'tipo_bloqueio'          => $this->input->post('tipo_bloqueio'), // Radio (valor será 'apos_exceder' ou 'imediato')
                    'percentual_excedente'   => $excedente,
                    'diana_descricao_proposta_prompt' => trim(strip_tags($this->input->post('diana_descricao_proposta_prompt'))),


                );
                // if (has_role('sysadmin')) {
                //     $data['id_resp_engenharia'] = $this->input->post('id_resp_engenharia');
                // } else {
                //     $data['id_resp_engenharia'] = sess_user_id();
                // }

                if (isset($logo_filename) && !empty($logo_filename)) {
                    $data['logo_filename'] = $logo_filename;
                }

                $id_empresa = $this->empresa_model->save($data);

                // var_dump($this->db->last_query()); 
                // var_dump($id_empresa); exit;

                $suframa_codes = $this->input->post('suframa_codes');
                $this->save_suframa_codes_empresa($suframa_codes, $id_empresa);

                // if ($this->input->is_set('id_segmento') && $this->input->post('id_segmento'))
                // {
                $this->load->model('permissao_atribuir_grupo_model');
                $this->permissao_atribuir_grupo_model->save_excecao_by_segmento($this->input->post('id_segmento'), $id_empresa);
                // }

                if ($this->input->is_set('classe')) {
                    $arr_classe = $this->input->post('classe');
                }

                if ($this->input->is_set('percentual')) {
                    $arr_percentual = $this->input->post('percentual');
                }

                if ($this->input->is_set('status')) {
                    $arr_status = $this->input->post('status');
                }

                if (!empty($this->input->post('paises'))) {

                    $paises =  $this->input->post('paises');

                    foreach ($paises as $pais) {
                        $data = array(
                            'id_pais'      => $pais,
                            'id_empresa'   => $id_empresa
                        );

                        $this->empresa_pais_model->save($data);
                    }
                }

                if (!empty($arr_classe) && !empty($arr_percentual) && !empty($arr_status)) {
                    $this->empresa_curva_attrs_model->remove($id_empresa);

                    foreach ($arr_classe as $key => $classe) {
                        if (empty($arr_percentual[$key]) || empty($arr_status[$key])) {
                            continue;
                        }

                        $dbdata_curva_atts = array(
                            'id_empresa' => $id_empresa,
                            'classe' => $classe,
                            'percentual' => $arr_percentual[$key],
                            'status' => $arr_status[$key]
                        );

                        $this->empresa_curva_attrs_model->save($dbdata_curva_atts);
                    }
                }

                $this->save_items_rel_diana($this->input->post(), $id_empresa);

                $this->empresa_model->update_concatenar_campos(
                    $this->input->post('concatenateItemsSlug'),
                    $this->input->post('concatenateItemsChecked'),
                    $id_empresa
                );

                $this->message_next_render('Sucesso! Empresa [<strong>' . $this->input->post('razao_social') . '</strong>] adicionada');

                redirect('cadastros/empresa');
            } else {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $this->include_js(array(
            'jquery-fileupload.js',
            'bootstrap-select/bootstrap-select.js',
            'empresa/addedit.js',
            'sweetalert.min.js'
        ));

        $this->include_css(array(
            'jquery-fileupload.css',
            'bootstrap-select/bootstrap-select.css',
            'sweetalert.css'
        ));

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Empresas', '/cadastros/empresa/');
        $this->breadcrumbs->push('Nova empresa', '/cadastros/empresa/novo');

        $data['funcoes_adicionais'] = array();

        $this->render('cadastros/empresa/novo', $data);
    }

    public function check_tolerancia($str)
    {
        if ($str > 0 && !$this->input->post('tipo_tolerancia')) {
            $this->form_validation->set_message('check_tolerancia', 'Você deve especificar um <strong>Tipo de Tolerância</strong>');
            return FALSE;
        }

        return TRUE;
    }

    public function check_pn_divergente()
    {
        $this->load->model('item_model');


        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $funcoes_adicionais = explode("|", $empresa->funcoes_adicionais);

        if (!in_array("validar_pn", $funcoes_adicionais)) {
            return false;
        }

        $post = $this->input->post();
        $data = [];
        $data['id_empresa'] = $empresa->id_empresa;
        $data['estabelecimento'] = $post['estabelecimento'];
        $data['ncm'] = $post['ncm'];
        $itens = $post['itens'];
        $ncm = [];

        $partnumbers = [];
        $estabelecimentos = [];

        if (!empty($itens)) {

            foreach ($itens as  $item) {

                $ncm[] =  $this->item_model->check_ncm_divergente($item, $data);
            }
            return response_json($ncm);
        }
        return false;
    }

    public function validate_emails($emails)
    {
        if (isset($emails)) {
            $email_array = explode(';', $emails);
            foreach ($email_array as $email) {
                $email = trim($email);
                if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    $this->form_validation->set_message('validate_emails', 'O campo "Destinatários TEC" contém um ou mais e-mails inválidos.');
                    return FALSE;
                }
            }
        }
        return TRUE;
    }

    public function format_decimal_for_db($value_from_post) {
        if ($value_from_post === null || $value_from_post === '') {
            return '0.00'; 
        }
        $value_with_dot = str_replace(',', '.', $value_from_post);
        $cleaned_value = preg_replace('/[^\d\.]/', '', $value_with_dot);
        
        if (is_numeric($cleaned_value)) {
            return (float) $cleaned_value; 
        }
        return null; 
    }

    public function converterDecimaisParaPonto($valor) {
     
            if (is_string($valor) && strpos($valor, ',') !== false) {
                return  $valorConvertido = str_replace(',', '.', str_replace(' ', '', $valor));
            }
   
        return $valor;
    }

    public function editar($id_empresa)
    {
        $this->load->model(array(
            'item_model',
            'empresa_model',
            'segmento_model',
            'empresa_curva_attrs_model',
            'suframa_model',
            'usuario_model',
            'app_ia_segmento_model',
            'pais_model',
            'perfil_model',
            'empresa_pais_model',
            'squad_model',
            'empresa_prioridades_model'
        ));

        $this->title = "Empresa &gt; Nova";

        $data = array();

        $data['segmentos'] = $this->segmento_model->get_entries();
        $data['paises'] = $this->pais_model->get_entries_out_brasil();
        $data['perfis'] = $this->perfil_model->get_entries();
        $data['squads'] = $this->squad_model->get_entries();
        $data['prioridades'] = $this->empresa_prioridades_model->get_entry($id_empresa, 'DESC');
        $this->empresa_curva_attrs_model->set_state('filter.id_empresa', $id_empresa);
        $data['empresa_curvas'] = $this->empresa_curva_attrs_model->get_entries();
        $data['is_edit'] = 1;
        try {
            $entry = $this->empresa_model->get_entry($id_empresa);
            $data['entry'] = $entry;
        } catch (Exception $e) {
            show_error($e->getMessage());
        }

        $paises_empresa = $this->empresa_pais_model->get_entries_by_empresa($id_empresa);

        $data['paises_empresa'] = $paises_empresa;

        $data['suframa_produtos'] = $this->suframa_model->get_rel_suframa_produtos_by_empresa_only_codes($id_empresa);

        $empresa = $this->empresa_model->get_entry($id_empresa);
        $current_campos_adicionais = $empresa->campos_adicionais;

        if ($this->input->post('submit')) {


            $valor_padrao_normal = $this->input->post('valor_padrao_normal') ?? [];
            $valor_quimico_normal = $this->input->post('valor_quimico_normal') ?? [];

            if (!empty($valor_padrao_normal) ) {
                foreach ($valor_padrao_normal as $k => $valor) {
                    if (!empty($valor)) {
                        $this->empresa_prioridades_model->update_value($k, $this->format_decimal_for_db($valor), 'padrao');
                    }
                }
            }

            if (!empty($valor_quimico_normal) ) {
                foreach ($valor_quimico_normal as $k => $valor) {
                    if (!empty($valor)) {
                        $this->empresa_prioridades_model->update_value($k, $this->format_decimal_for_db($valor), 'quimico');
                    }
                }
            }

            $suframa_codes = $this->input->post('suframa_codes');
            $this->save_suframa_codes_empresa($suframa_codes, $id_empresa);

            $this->load->library('form_validation');

            $this->form_validation->set_rules('razao_social', 'Razão Social', 'trim|required');
            $this->form_validation->set_rules('nome_fantasia', 'Nome Fantasia', 'trim');
            $this->form_validation->set_rules('tolerancia', 'Tolerância', 'trim|callback_check_tolerancia');
            $this->form_validation->set_rules('tipo_tolerancia', 'Tipo Tolerância', 'trim');
            $this->form_validation->set_rules('estabelecimento_default', 'Estabelecimento', 'trim|required');

            if (in_array("destinatatio_tec", $this->input->post('funcoes_adicionais'))) {
                $this->form_validation->set_rules('destinatarios_tec', 'Destinatarios TEC', 'trim|required|callback_validate_emails');
            } else {
                $this->form_validation->set_rules('destinatarios_tec', 'Destinatarios TEC', 'trim|callback_validate_emails');
            }


            $unique = '';

            if ($entry->cnpj != $this->input->post('cnpj')) {
                $unique = "|is_unique[empresa.cnpj]";
            }

            $this->form_validation->set_rules('cnpj', 'CNPJ', 'trim|required' . $unique);

            $logo_filename = NULL;
            $valid_form = $this->form_validation->run();

            if ($valid_form == TRUE) {

                if (isset($_FILES['arquivo']) && $_FILES['arquivo']['size'] > 0) {
                    $config['upload_path'] = FCPATH . config_item('upload_logo_empresa_path');
                    $config['allowed_types'] = 'gif|jpg|png';
                    $config['max_width'] = '800';
                    $config['max_height'] = '600';
                    $config['encrypt_name'] = TRUE;

                    $this->load->library('upload');
                    $this->upload->initialize($config);

                    $valid_form = $this->upload->do_upload('arquivo');

                    if ($valid_form) {
                        $upload_data  = $this->upload->data();
                        $logo_filename = $upload_data['file_name'];

                        if (file_exists($config['upload_path'] . $entry->logo_filename)) {
                            unlink($config['upload_path'] . $entry->logo_filename);
                        }
                    }
                }


                if ($valid_form) {
                    if (!empty($this->input->post('campos_adicionais'))) {
                        $campos_adicionais = implode('|', $this->input->post('campos_adicionais'));
                    } else {
                        $campos_adicionais = NULL;
                    }

                    $was_owner_present = strpos($current_campos_adicionais, 'owner') !== false;
                    $is_owner_present = strpos($campos_adicionais, 'owner') !== false;

                    if ($was_owner_present && !$is_owner_present) {
                        // Se a condição anterior for verdadeira, utilize o método IsThereAnItemWithSpecificStatuses()
                        // para verificar a presença dos itens com os status específicos
                        $has_specific_statuses = $this->empresa_model->IsThereAnItemWithSpecificStatuses($id_empresa);

                        if ($has_specific_statuses) {
                            // var_dump($has_specific_statuses);
                            // exit;
                            // Exibir uma mensagem de erro ou redirecionar o usuário para a página apropriada
                            $err = "<h4>Erro ao atualizar os campos adicionais da empresa</h4>";
                            $err .= "<p>Existem itens com status específicos vinculados a esta empresa. Por favor, verifique-os antes de remover o campo 'Owner'.</p>";
                            $this->message_next_render($err, 'error');

                            redirect('cadastros/empresa/editar/' . $id_empresa);
                        }
                    }

                    if (!empty($this->input->post('funcoes_adicionais'))) {
                        $funcoes_adicionais = implode('|', $this->input->post('funcoes_adicionais'));
                    } else {
                        $funcoes_adicionais = NULL;
                    }
                    $ativo = !empty($this->input->post('ativo')) ? 1 : 0;
                    
                    $primeira_notificacao = !empty($this->input->post('percentual_primeira_notificacao')) 
                    ? str_replace('%', '', $this->input->post('percentual_primeira_notificacao')) 
                    : null;
                
                    $segunda_notificacao = !empty($this->input->post('percentual_segunda_notificacao')) 
                        ? str_replace('%', '', $this->input->post('percentual_segunda_notificacao')) 
                        : null;
                    
                        $excedente = !empty($this->input->post('percentual_excedente'))            ? str_replace('%', '', $this->input->post('percentual_excedente'))            : null;

                    if (!empty($this->input->post('servicos_contratados'))) {
                        $servicos_contratados = implode('|', $this->input->post('servicos_contratados'));
                    } else {
                        $servicos_contratados = NULL;
                    }

                    $data = array(
                        'razao_social'              => $this->input->post('razao_social'),
                        'nome_fantasia'             => $this->input->post('nome_fantasia'),
                        'cnpj'                      => $this->input->post('cnpj'),
                        'id_segmento'               => $this->input->post('id_segmento'),
                        'retira_icms_preco'         => (int) $this->input->post('retira_icms_preco'),
                        'retira_pis_preco'          => (int) $this->input->post('retira_pis_preco'),
                        'retira_cofins_preco'       => (int) $this->input->post('retira_cofins_preco'),
                        'tipo_tolerancia'           => $this->input->post('tipo_tolerancia'),
                        'tolerancia'                => $this->input->post('tolerancia'),
                        'descricao_max_caracteres'  => $this->input->post('descricao_max_caracteres'),
                        'id_gerente_de_projetos'    => $this->input->post('id_gerente_de_projetos'),
                        'updated_at'                => date('Y-m-d H:i:s'),
                        'campos_adicionais'         => $campos_adicionais,
                        'funcoes_adicionais'        => $funcoes_adicionais,
                        'servicos_contratados'      => $servicos_contratados,
                        'acordos_premissas'         => $this->input->post('acordos'),
                        'porta_a_porta'             => $this->input->post('venda_porta_a_porta'),
                        'estabelecimento_default'   => $this->input->post('estabelecimento_default'),
                        'recebe_email_pendencias'   => $this->input->post('recebe_email_pendencias'),
                        'id_resp_fiscal'            => $this->input->post('id_resp_fiscal'),
                        'app_a_seguimento'          => $this->input->post('app_a_seguimento'),
                        'app_b_seguimento'          => $this->input->post('app_b_seguimento'),
                        'app_a_cod'                 => $this->input->post('app_a_cod'),
                        'app_b_cod'                 => $this->input->post('app_b_cod'),
                        'app_a_modelo'              => $this->input->post('app_a_modelo'),
                        'app_b_modelo'              => $this->input->post('app_b_modelo'),
                        'separador_pesquisa'        => $this->input->post('separador_pesquisa'),
                        'ativo'                     => $ativo,
                        'id_squad'                  => $this->input->post('id_squad'),
                        'destinatarios_tec'         => $this->input->post('destinatarios_tec'),
                        'destinatarios_revisao_pucomex'         => $this->input->post('destinatarios_revisao_pucomex'),
                        'perfil_usuario_padrao_id'  => !empty($this->input->post('perfil_usuario_padrao_id')) ? $this->input->post('perfil_usuario_padrao_id') : null,

                        'habilitar_uso_franquia' => (int) $this->input->post('habilitar_uso_franquia'), // Checkbox
                        'quantidade_franquia_mensal' => $this->input->post('quantidade_franquia_mensal'),
                        'habilitar_cobrancas_adicionais' => (int) $this->input->post('habilitar_cobrancas_adicionais'), // Checkbox
                        'valor_padrao_default'   => $this->converterDecimaisParaPonto($this->input->post('valor_padrao_default')),
                        'valor_quimico_default'  => $this->converterDecimaisParaPonto($this->input->post('valor_quimico_default')),
                        'habilitar_notificacoes' => (int) $this->input->post('habilitar_notificacoes'), // Checkbox
                        'destinatarios_excedente'     => $this->input->post('destinatarios_excedente'),
                        'percentual_primeira_notificacao'     => $primeira_notificacao,
                        'percentual_segunda_notificacao'     => $segunda_notificacao,
                        'habilitar_bloqueio'     => (int) $this->input->post('habilitar_bloqueio'), // Checkbox
                        'tipo_bloqueio'          => $this->input->post('tipo_bloqueio'), // Radio (valor será 'apos_exceder' ou 'imediato')
                        'percentual_excedente'   => $excedente,
                        'diana_descricao_proposta_prompt' => trim(strip_tags($this->input->post('diana_descricao_proposta_prompt'))),

                    );


                    if ($this->input->is_set('classe')) {
                        $arr_classe = $this->input->post('classe');
                    }

                    if ($this->input->is_set('percentual')) {
                        $arr_percentual = $this->input->post('percentual');
                    }

                    if ($this->input->is_set('status')) {
                        $arr_status = $this->input->post('status');
                    }

                    if (!empty($this->input->post('paises'))) {
                        $this->empresa_pais_model->remove_entry_all_by_empresa($id_empresa);

                        $paises =  $this->input->post('paises');

                        foreach ($paises as $pais) {
                            $data_paises = array(
                                'id_pais'      => $pais,
                                'id_empresa'   => $id_empresa
                            );
                            $this->empresa_pais_model->save($data_paises);
                        }
                    } else {
                        $this->empresa_pais_model->remove_entry_all_by_empresa($id_empresa);
                    }

                    if (!empty($arr_classe) && !empty($arr_percentual) && !empty($arr_status)) {
                        $this->empresa_curva_attrs_model->remove($id_empresa);

                        foreach ($arr_classe as $key => $classe) {
                            if (empty($arr_percentual[$key]) || empty($arr_status[$key])) {
                                continue;
                            }

                            $dbdata_curva_atts = array(
                                'id_empresa' => $id_empresa,
                                'classe' => $classe,
                                'percentual' => $arr_percentual[$key],
                                'status' => $arr_status[$key]
                            );

                            $this->empresa_curva_attrs_model->save($dbdata_curva_atts);
                        }
                    }

                    $this->save_items_rel_diana($this->input->post(), $entry->id_empresa);

                    $this->empresa_model->update_concatenar_campos(
                        $this->input->post('concatenateItemsSlug'),
                        $this->input->post('concatenateItemsChecked'),
                        $entry->id_empresa
                    );

                    if (!empty($logo_filename)) {
                        $data['logo_filename'] = $logo_filename;
                    }

                    $this->empresa_model->save($data, array('id_empresa' => $entry->id_empresa));

                    $this->message_next_render('Sucesso! Empresa [<strong>' . $this->input->post('razao_social') . '</strong>] atualizada');

                    // force refresh content
                    customer_can('ii', TRUE);
                    redirect('cadastros/empresa');
                } else {
                    $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . $this->upload->display_errors('<li>', '</li>') . "</ul>";
                    $this->message_on_render($err, 'error');
                }
            } else {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . validation_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            }
        }

        $data['gerentes_de_projeto'] = $this->usuario_model->get_gps_by_empresa($id_empresa);
        $data['usuarios'] = $this->usuario_model->get_entries_by_role('fiscal', null, false, null, 'ativo', [$entry->id_resp_fiscal]);
        $data['usuarios_ativos'] = array_values(array_filter($data['usuarios'], function($usuario) {
            return $usuario->status == 1;
        }));
        $data['usuarios_inativos'] = array_values(array_filter($data['usuarios'], function($usuario) {
            return $usuario->status == 0;
        }));

        $this->include_js(array(
            'jquery-fileupload.js',
            'bootstrap-select/bootstrap-select.js',
            'empresa/addedit.js',
            'sweetalert.min.js'
        ));

        $this->include_css(array(
            'jquery-fileupload.css',
            'bootstrap-select/bootstrap-select.css',
            'sweetalert.css'
        ));

        //segmentos
        $data['segmentos_ia'] = $this->app_ia_segmento_model->get_all_entries();
        $data['users_status_change'] = $this->item_model->get_config_status($id_empresa);

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Empresas', '/cadastros/empresa/');
        $this->breadcrumbs->push('Editar empresa', '/cadastros/empresa/editar/' . $id_empresa);

        // force refresh content
        customer_can('ii', TRUE);

        $this->render('cadastros/empresa/editar', $data);
    }

    public function get_campos_concat($id_empresa = null)
    {
        return response_json($this->empresa_model->get_concatenar_campos($id_empresa));
    }

    private function save_items_rel_diana($post, $id_empresa)
    {
        if (empty($id_empresa)) {
            return false;
        }


        $itemsDiana = [];
        if (!empty($post['itemsSlug'])) {
            $itemsDiana = $post['itemsSlug'];
        }

        if (!empty($itemsDiana)) {
            $this->empresa_model->remove_rel_diana_inf(array(
                'id_empresa' => $id_empresa
            ));

            $itemsChecked = [];
            if (!empty($post['itemsChecked'])) {
                $itemsChecked = $post['itemsChecked'];
            }


            foreach ($itemsDiana as $key => $slug) {
                $this->empresa_model->rel_diana_inf(array(
                    'id_empresa' => $id_empresa,
                    'index' => $key,
                    'slug' => $slug,
                    'checked' => isset($itemsChecked[$key]) && !empty($itemsChecked[$key]) ? 1 : 0
                ));
            }
        }
    }

    public function xhr_get_suframa_produtos()
    {
        $search_value = $this->input->get('searchValue');
        $ignore_codes = $this->input->get('ignorarCodigos');

        $this->load->model('suframa_model');

        $this->suframa_model->set_state('ignore_codes', $ignore_codes);
        $suframa_itens = $this->suframa_model->get_suframa_produtos($search_value);

        response_json($suframa_itens);
    }

    public function save_suframa_codes_empresa($codes, $id_empresa)
    {
        $this->load->model('suframa_model');

        if (!empty($id_empresa && $codes)) {
            $this->suframa_model->save_rel_suframa_produtos($id_empresa, $codes);
        }
    }

    public function save_suframa_produtos()
    {
        $this->load->model('suframa_model');

        $id_empresa = $this->input->post('id_empresa');
        $codigos = $this->input->post('codigos');

        if (!empty($id_empresa && $codigos)) {
            $this->suframa_model->save_rel_suframa_produtos($id_empresa, $codigos);
        }

        return response_json('');
    }
}
