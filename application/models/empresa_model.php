<?php

class Empresa_model extends MY_Model
{

    public $_table = 'empresa';
    public $_table_sort = 'empresa_diana_inf';
    public $_table_concat = 'empresa_concat';
    private $_campos_concat = array(
        "descricao" => array(
            "name" => "Descrição",
            "slug" => "descricao",
            "checked" => true,
            "ordem" => 1,
            "id" => 1,
        ),
        // "descricao_completa" => array(
        //     "name" => "Descrição completa",
        //     "slug" => "descricao_completa",
        //     "checked" => true,
        //     "ordem" => 2,
        //     "id" => 2,
        // ),
        // "descricao_proposta_resumida" => array(
        //     "name" => "Descrição resumida",
        //     "slug" => "descricao_proposta_resumida",
        //     "checked" => true,
        //     "ordem" => 3,
        //     "id" => 3,
        // ),
        "subsidio" => array(
            "name" => "Subsídio",
            "slug" => "subsidio",
            "checked" => true,
            "ordem" => 2,
            "id" => 2,
        ),
        "suframa" => array(
            "name" => "Suframa Descrição",
            "slug" => "suframa",
            "checked" => true,
            "ordem" => 3,
            "id" => 3,
        ),
        // "suframa_destaque" => array(
        //     "name" => "Suframa Destaque",
        //     "slug" => "suframa_destaque",
        //     "checked" => true,
        //     "ordem" => 6,
        //     "id" => 6,
        // ),
        "funcao" => array(
            "name" => "Função",
            "slug" => "funcao",
            "checked" => true,
            "ordem" => 4,
            "id" => 4,
        ),
        "aplicacao" => array(
            "name" => "Aplicação",
            "slug" => "aplicacao",
            "checked" => true,
            "ordem" => 5,
            "id" => 5,
        ),
        "material_constitutivo" => array(
            "name" => "Material Constitutivo",
            "slug" => "material_constitutivo",
            "checked" => true,
            "ordem" => 6,
            "id" => 6,
        ),
        "marca" => array(
            "name" => "Marca",
            "slug" => "marca",
            "checked" => true,
            "ordem" => 7,
            "id" => 7,
        ),
        "nve" => array(
            "name" => "NVE",
            "slug" => "nve",
            "checked" => true,
            "ordem" => 8,
            "id" => 8,
        ),
        "li" => array(
            "name" => "LI",
            "slug" => "li",
            "checked" => true,
            "ordem" => 9,
            "id" => 9,
        ),
        "cod_importador" => array(
            "name" => "Código Importador",
            "slug" => "cod_importador",
            "checked" => true,
            "ordem" => 10,
            "id" => 10,
        ),
        "cod_fornecedor" => array(
            "name" => "Código Fornecedor",
            "slug" => "cod_fornecedor",
            "checked" => true,
            "ordem" => 11,
            "id" => 11,
        )
    );

    public function get_all_entries()
    {
        $this->db->order_by('razao_social', 'ASC');

        $query = $this->db->get($this->_table);
        return $query->result();
    }

    public function get_funcoes_adicionais($role, $id_empresa)
    {
        $this->db->where('id_empresa', $id_empresa);
        $query = $this->db->get($this->_table);
        $empresa = $query->row();
        $funcoes_adicionais_arr = explode('|', $empresa->funcoes_adicionais);

        return $funcoes_adicionais_arr;
    }

    public function get_total_entries()
    {
        $this->db->select('count(distinct e.id_empresa) as total', false);

        if ($nome_cnpj = $this->get_state('filter.nome_cnpj')) {
            $this->db->like('e.nome_fantasia', $nome_cnpj);
            $this->db->or_like('e.cnpj', $nome_cnpj);
        }

        if ($id_segmento = $this->get_state('filter.id_segmento')) {
            $this->db->where("e.id_segmento", $id_segmento);
        }

        $query = $this->db->get($this->_table . ' e');

        return $query->row()->total;
    }

    public function get_entries($limit = NULL, $offset = NULL)
    {
        $this->db->select('e.*, s.descricao AS segmento_descricao');
        $this->db->join('segmento s', 's.id_segmento = e.id_segmento', 'left');

        if ($order_by = $this->get_state('filter.order_by')) {
            $this->db->order_by($order_by);
        }

        if ($order_by = $this->get_state('ativo')) {
            $this->db->where('ativo', '1');
        }
        if ($nome_cnpj = $this->get_state('filter.nome_cnpj')) {
            $this->db->like('e.nome_fantasia', $nome_cnpj);
            $this->db->or_like('e.cnpj', $nome_cnpj);
        }

        if ($id_segmento = $this->get_state('filter.id_segmento')) {
            $this->db->where("s.id_segmento", $id_segmento);
        }

        if ($recebe_email = $this->get_state('filter.recebe_email_pendencias')) {
            $this->db->where('recebe_email_pendencias', 1);
        }

        $query = $this->db->get($this->_table . ' e', $limit, $offset);
        return $query->result();
    }

    public function get_owners_by_empresa($empresa, $owner_user = NULL)
    {
        //Remove os pontos e barras do CNPJ da empresa e pega apenas os 8 primeiros dígitos
        $cnpjEmpresa = substr(preg_replace('/[^0-9]/', '',
            $empresa->cnpj
        ), 0, 8);

        if (empty($cnpjEmpresa)) {
            return array();
        }

        if ($owner_user) {
            if (is_array($owner_user)) {
                $owner_user = implode("', '", $owner_user);
                $owner_user_condition = "AND o.codigo IN ('$owner_user')";
            } else {
                $owner_user_condition = "AND o.codigo = '$owner_user'";
            }
        } else {
            $owner_user_condition = "";
        }
        
        $sql = "SELECT o.*, 
                IFNULL(GROUP_CONCAT(' ' ,u.nome),'') as nomes,
                IFNULL(GROUP_CONCAT(' ' ,u.email),'') as emails
                FROM owner o
                LEFT JOIN owner_usuario ou ON o.id_owner = ou.id_owner AND ou.responsavel_gestor = 1
                LEFT JOIN usuario u ON ou.id_usuario = u.id_usuario
                WHERE o.ativo = 1
                AND LEFT (REPLACE(o.cnpj_raiz, '.', ''), 8) = '$cnpjEmpresa'
                $owner_user_condition
                GROUP BY o.id_owner
                ORDER BY o.descricao ASC";
    
        $resultado = $this->db->query($sql);
        
        return $resultado->result();
    }

    public function check_imported_item_permission($id_empresa)
    {
        $sql = $this->db->query("SELECT * FROM empresa 
                WHERE 
                    (id_empresa = '{$id_empresa}')
                    AND (funcoes_adicionais LIKE '%|item_importado_default|%' 
                    OR  funcoes_adicionais LIKE 'item_importado_default|%' 
                    OR  funcoes_adicionais LIKE '%|item_importado_default' 
                    OR  funcoes_adicionais = 'item_importado_default');");

                if ($sql->num_rows() > 0) {
                    return true;
                }
 
                return false;
    }
    
    public function set_info_mail_settings($mes, $ano, $tipo)
    {
        $id_empresa = sess_user_company();
        $key = $ano.$mes.$tipo;

        $this->db->set('info_notificacao', $key);
        $this->db->where('id_empresa', $id_empresa);
        return $this->db->update($this->_table);

    }

    public function enviarEmailFranquia($franquia, $empresa, $franquia_usada, $notificacao_tipo)
	{
        $email_data['base_url'] = config_item('online_url');
        $email_data['franquia'] = $franquia;
        $email_data['franquia_usada'] = $franquia_usada;
        $email_data['notificacao_tipo'] = $notificacao_tipo;
        $body = $this->load->view('templates/notificacao_franquia', $email_data, TRUE);
 
        $emails = explode(';', $empresa->destinatarios_excedente);
        $count_emails = 0;
        foreach($emails as $email){

            $this->load->library('email');

            $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
            $this->email->to($email);
            $this->email->subject('[Gestão Tarifária] - Notificação de franquia');
            $this->email->message($body);

            $this->email->send();

            $count_emails ++;
        }

        return $count_emails;
	}

    public function mail_settings_finance()
    {
        $dados_empresa = $this->get_settings_finance(['habilitar_uso_franquia' => 1, 'habilitar_notificacoes' => 1]);
    
        if (!empty($dados_empresa))
        {
 
            $data_inicial = date('Y-m-01');  
            $data_final = date('Y-m-t');    
            // $data_inicial = '2025-01-01';
            // $data_final = '2025-01-31';
            $totais = $this->item_model->get_totais_franquia($data_inicial, $data_final);
            if (!empty($totais))
            {

                $totais_criados = $totais->Total_Itens_Criados + $totais->Total_Itens_Quimicos_Criados;
                $totais_trabalhados = $totais->Total_Itens_Reclassificados + $totais->Total_Itens_Quimicos_Reclassificados;
                $totais_processados = $totais_trabalhados + $totais_criados;

                $ano = date('Y', strtotime($data_final));
                $mes = date('m', strtotime($data_final));

                if ($dados_empresa->info_notificacao == $ano.$mes.'2')
                {
                    return true;
                } 

                $valor = (float) $dados_empresa->percentual_segunda_notificacao;
                    
                $percentual_processado = (float) ($totais_processados  / $dados_empresa->quantidade_franquia_mensal ) * 100;

                if ((int) $dados_empresa->percentual_segunda_notificacao <=  $percentual_processado  )
                {
 

                    if (empty($dados_empresa->info_notificacao) || $dados_empresa->info_notificacao != $ano.$mes.'2')
                    {
               
                        $this->enviarEmailFranquia($dados_empresa->percentual_segunda_notificacao, $dados_empresa, $percentual_processado, '2');
                        return $this->set_info_mail_settings($mes, $ano, 2);
                        
                    }
                }
 
                if ((int) $dados_empresa->percentual_primeira_notificacao <=   $percentual_processado  && (int) $dados_empresa->percentual_segunda_notificacao >  $percentual_processado )
                {
 
                    if (empty($dados_empresa->info_notificacao) || $dados_empresa->info_notificacao != $ano.$mes.'1')
                    {
               
                        $this->enviarEmailFranquia($dados_empresa->percentual_primeira_notificacao, $dados_empresa, $percentual_processado, '1');
                        return $this->set_info_mail_settings($mes, $ano, 1);
    
                    }
                }

            }
        }
    }

    public function get_settings_finance($params = null, $id_empresa = null)
    {

        if (empty($id_empresa)) {
            $id_empresa = sess_user_company();
        }
        
        if (!empty($params)) {
            $this->db->where($params);
        }

        $this->db->where('id_empresa', $id_empresa);
        $query = $this->db->get($this->_table);
        $empresa = $query->row();

        return $empresa;
    }
       
    // public function get_permission_finance($tipo, $empresa)
    // {
    //     if (empty($tipo)) {
    //         return false;
    //     }
    //     if (!empty($empresa)) {
    //         if ($empresa->habilitar_uso_franquia == 1) {
    
    //             if ($tipo == 'habilitar_cobrancas_adicionais') {
                   
    //                 if ($empresa->habilitar_cobrancas_adicionais == 1) {
    //                     return true;
    //                 } else {
    //                     return false;
    //                 }

    //             } else if ($tipo == 'habilitar_notificacoes_check') {
    //                 if ($empresa->habilitar_notificacoes_check == 1) {
    //                     return true;
    //                 } else {
    //                     return false;
    //                 }
    //             } else if ($tipo == 'habilitar_bloqueio_check') {
    //                 if ($empresa->habilitar_bloqueio_check == 1) {
    //                     return true;
    //                 } else {
    //                     return false;
    //                 }
    //             }

    //         } else {
    //             return false;
    //         }
    //     }
    // }

    public function get_entry($id_empresa, $where = null)
    {
        $this->db->where('id_empresa', $id_empresa);

        if (!empty($where) )
        {
            $this->db->where($where);
        }
        $query = $this->db->get($this->_table);
        if ($query->num_rows() > 0) {
            return $query->row();
        }

        throw new Exception('Código de empresa inexistente.');
    }

    public function get_entry_cnpj_raiz($id_empresa)
    {
        $this->db->select("SUBSTR(REPLACE(cnpj, '.', ''), 1, 8) as cnpj_raiz", false); 
        $this->db->from($this->_table);
        $this->db->where('id_empresa', $id_empresa);
        $query = $this->db->get();

        if ($query->num_rows() > 0) {
            $empresa = $query->row();

            return  $empresa->cnpj_raiz;
        }

        return NULL;
    }

    public function get_full_entry($id_empresa) 
    {
        $this->db->where('id_empresa', $id_empresa);
        $query = $this->db->get($this->_table);
        if ($query->num_rows() > 0) {
            $empresa = $query->row();

            $this->db->where('id_empresa', $empresa->id_empresa);
            $query = $this->db->get($this->_table_sort);
            $empresa->listSort = $query->result();

            return  $empresa;
        }

        throw new Exception('Código de empresa inexistente.');
    }

    public function get_homologacao_by_id_empresa($id_empresa)
    {
        $this->db->where('id_empresa', $id_empresa);
        $query = $this->db->get($this->_table);

        if ($query->num_rows() > 0) {
            $row = $query->row();
            $homologacao = explode('|', $row->funcoes_adicionais);
            $data = array();
            if (in_array('homologacao_fiscal', $homologacao)) {
                $data['homologacao_fiscal'] = TRUE;
            }
            if (in_array('homologacao_engenharia', $homologacao)) {
                $data['homologacao_engenharia'] = TRUE;
            }
            return $data;
        }
        throw new Exception('Código de empresa inexistente.');
    }

    public function remove($id_list)
    {
        if (is_array($id_list) && count($id_list) > 0) {
            $this->db->where_in('id_empresa', $id_list);
            $query = $this->db->get($this->_table);

            foreach ($query->result() as $result) {

                $this->db->where('id_empresa',$result->id_empresa);
                $this->db->delete('empresa_pais');

                if ($logo = $this->get_logo_filepath($result->logo_filename)) {
                    unlink($logo);
                }
            }

            $this->db->where_in('id_empresa', $id_list);
            return $this->db->delete($this->_table);
        }

        return FALSE;
    }

    public function get_logo_url($logo_filename)
    {
        if (!empty($logo_filename) && is_file(FCPATH . config_item('upload_logo_empresa_path') . $logo_filename)) {
            return base_url(config_item('upload_logo_empresa_path') . $logo_filename);
        }
    }

    public function get_logo_filepath($logo_filename)
    {
        if (!empty($logo_filename) && is_file(FCPATH . config_item('upload_logo_empresa_path') . $logo_filename)) {
            return FCPATH . config_item('upload_logo_empresa_path') . $logo_filename;
        }
    }

    public function save_gp($id_usuario, $id_empresa)
    {
        if (!empty($id_usuario)) {
            $this->db->where('id_gerente_de_projetos', $id_usuario);
            $this->db->update($this->_table, array('id_gerente_de_projetos' => NULL));
        }

        if (is_array($id_empresa)) {
            foreach ($id_empresa as $emp) {
                $this->db->where('id_empresa', $emp);
                $this->db->update($this->_table, array('id_gerente_de_projetos' => $id_usuario));
            }
        } else {
            $this->db->where('id_empresa', $id_empresa);
            $this->db->update($this->_table, array('id_gerente_de_projetos' => $id_usuario));
        }
    }

    public function get_entry_by_cnpj($cnpj)
    {
        $this->db->where('cnpj', $cnpj);
        $query = $this->db->get($this->_table);
        if ($query->num_rows() > 0) {
            return $query->row();
        }
    }

    public function get_entry_by_cnpj_raiz($cnpj) 
    {
        $this->db->like('cnpj', $cnpj, 'after');
        $query = $this->db->get($this->_table);

        if ($query->num_rows() > 0) {
            return $query->result();
        }
    }

    public function get_tags_view($id_empresa = NULL, $tag = NULL, $limit = NULL)
    {
        $this->db->where('id_empresa', $id_empresa);
        $this->db->like('tag', $tag, 'after');
        $query = $this->db->get('v_tag_empresa', $limit);

        return $query->result();
    }

    public function get_estabelecimentos($id_empresa)
    {
        $data = array();
        
        $sql = "SELECT * FROM
                    v_empresa_estabelecimento
                WHERE id_empresa = $id_empresa
                    AND trim(estabelecimento) != '' 
                ORDER BY
                    padrao desc, estabelecimento ASC";

        $query = $this->db->query($sql);

        foreach ($query->result() as $row) {
            $data[] = $row->estabelecimento;
        }

        return $data;
    }

    public function update_values($dbdata, $where)
    {
        if (!empty($where)) {
            return $this->db->update($this->_table, $dbdata, $where);
        }
    }

    public function remove_rel_diana_inf($where) {
        if (!empty($where) && !empty($where['id_empresa'])) {
            $this->db->where($where);
            $this->db->delete($this->_table_sort);
        }   
    }

    public function rel_diana_inf($dbdata)
    {
        $this->db->insert($this->_table_sort, $dbdata);
    }

    public function remove_rel_concat_campos($where)
    {
        if (!empty($where) && !empty($where['id_empresa'])) {
            $this->db->where($where);
            $this->db->delete($this->_table_concat);
        }
    }

    public function update_concatenar_campos($campos = array(), $camposChecked = array(), $id_empresa)
    {
        if (empty($id_empresa)) {
            return false;
        }

        if (!empty($campos)) {
            $this->remove_rel_concat_campos(array(
                'id_empresa' => $id_empresa
            ));
            
            foreach($campos as $key => $slug) {
                $this->db->insert($this->_table_concat, array(
                    'id_empresa' => $id_empresa,
                    'ordem' => $key,
                    'slug' => $slug,
                    'checked' => isset($camposChecked[$key]) && !empty($camposChecked[$key]) ? 1 : 0,
                ));
            }

            return true;
        }

        return false;
    }

    public function get_concatenar_campos($id_empresa)
    {
        $result = array();

        if (!empty($id_empresa)) {
            $this->db->where('id_empresa', $id_empresa);
            $this->db->order_by('ordem', 'ASC');
            $query = $this->db->get($this->_table_concat);
            $result = $query->result();
        }

        $data = array();

        if (empty($result)) {
            foreach($this->_campos_concat as $campo) {
                $data[] = $campo;
            }
        } else {
            $data = array_map(function($item) {
                $item->name = $this->_campos_concat[$item->slug]['name'];
                $item->checked = !empty($item->checked);
                return $item;
            }, $result);
        }

        return $data;
    }

    public function get_unidade_negocio_by_empresa($empresa)
    {
        $cnpjEmpresa = substr(preg_replace('/[^0-9]/', '', $empresa->cnpj), 0, 8);

        $sql = "SELECT u.*
            FROM unidade_negocio u
            JOIN empresa e ON SUBSTR(REPLACE(e.cnpj, '.', ''), 1, 8) = SUBSTR(REPLACE(u.cnpj_raiz, '.', ''), 1, 8)
            WHERE SUBSTR(REPLACE(u.cnpj_raiz, '.', ''), 1, 8) = ?
            AND u.ativo = 1
            ORDER BY u.descricao ASC";

        $resultado = $this->db->query($sql, array($cnpjEmpresa));

        return $resultado->result();
    }

    public function IsThereAnItemWithSpecificStatuses($id_empresa)
    {
        $statusNovos = [10, 11]; // 10 = Homologado em Revisao e 11 = Revisar Informacoes Tecnicas

        $this->db->where('id_empresa', $id_empresa);
        $this->db->where_in('id_status', $statusNovos);
        $query = $this->db->get('item');

        if ($query->num_rows() > 0) {
            return true;
        }

        return false;

    }

    /**
     * Método para trazer as empresas que tem nas funcoes_adicionais o 'status_triagem_diana'
     * 
     * @return array
     * 
     */
    public function get_empresas_with_status_triagem_diana()
    {
        $this->db->where("funcoes_adicionais LIKE '%status_triagem_diana%'", null, false);
        $query = $this->db->get('empresa');

        return $query->result();
    }

    /**
     * Método para trazer as empresas que tem nas funcoes_adicionais 'has_diana_descricao_proposta' (com diana_descricao_proposta_prompt preenchido) ou 'has_diana_descricao_supercompleta'
     * 
     * @return array
     * 
     */
    public function get_empresas_with_diana_descricao_proposta_or_supercompleta()
    {
        $this->db->where("funcoes_adicionais LIKE '%has_diana_descricao_proposta%' AND diana_descricao_proposta_prompt IS NOT NULL AND diana_descricao_proposta_prompt != ''", null, false);
        $this->db->or_where("funcoes_adicionais LIKE '%has_diana_descricao_supercompleta%'", null, false);
        $query = $this->db->get('empresa');

        return $query->result();
    }

    public function get_empresa_prompt_descricao_completa_e_resumida($company_id)
    {
        $this->db->where('id_empresa', $company_id);
        $this->db->where("funcoes_adicionais LIKE '%has_diana_descricao_proposta%' AND diana_descricao_proposta_prompt IS NOT NULL AND diana_descricao_proposta_prompt != ''", null, false);
        $query = $this->db->get('empresa');

        return $query->row()->diana_descricao_proposta_prompt ?? null;
    }
}
