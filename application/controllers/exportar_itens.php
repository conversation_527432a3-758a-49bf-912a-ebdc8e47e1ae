<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

/**
 * Exportar itens
 * @property CI_Loader $load
 * @property MY_Input $input
 * @property CI_Output $output
 * @property CI_DB_query_builder $db
 * @property CI_Session $session
 * @property CI_URI $uri
 * @property CI_Form_validation $form_validation
 * @property CI_Config $config
 * @property CI_Zip $zip
 * @property CI_Email $email
 * @property CI_Upload $upload
 * @property Item_model $item_model
 * @property Cad_item_model $cad_item_model
 * @property Empresa_model $empresa_model
 */
class Exportar_itens extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

    }

    public function exportar_mestre()
    {
        ini_set('memory_limit', -1);

        $this->load->model(array('comex_model','item_model', "cad_item_model", "ex_tarifario_model", 'empresa_model'));

        //Status
        $status = $this->input->post('status') ? $this->input->post('status') : null;   
        if (!empty($status)) {
            $this->item_model->set_state('filter.status', $status);
        }else{
            $this->item_model->unset_state('filter.status');
        }

        //Pacote/Evento
        $evento = $this->input->post('evento') ? $this->input->post('evento') : NULL;
        if (!empty($evento)) {
            $this->item_model->set_state('filter.evento', $evento);
        }else{
            $this->item_model->unset_state('filter.evento');
        }

        //Data Inicial Criação
        $data_ini_str = $this->input->post('data_ini') ? $this->input->post('data_ini') :  NULL;
        if (!empty($data_ini_str)) {
            $data_ini = str_replace('/', '-', $data_ini_str);
            $data_ini = date('Y-m-d', strtotime($data_ini));

            $this->item_model->set_state('filter.data_ini', $data_ini);
        }else{
            $this->item_model->unset_state('filter.data_ini');
        }

        //Data Final Criação
        $data_fim_str = $this->input->post('data_fim') ? $this->input->post('data_fim') :  NULL;
        if (!empty($data_fim_str)) {
            $data_fim = str_replace('/', '-', $data_fim_str);
            $data_fim = date('Y-m-d', strtotime($data_fim));

            $this->item_model->set_state('filter.data_fim', $data_fim);
        }else{
            $this->item_model->unset_state('filter.data_fim');
        }

        //Status Exportação
        $statusExportacao = $this->input->post('statusExportacao') ? $this->input->post('statusExportacao') : NULL;
        if (!empty($statusExportacao) && $statusExportacao != '-1') {
            $this->item_model->set_state('filter.statusExportacao', $statusExportacao);
        }else{
            $this->item_model->unset_state('filter.statusExportacao');
        }

        //Owner
        $owner_filter = [];
        $owner_filter = $this->input->post('owner') ? $this->input->post('owner') : NULL;
        if (is_array($owner_filter) && !in_array(-1, $owner_filter)) {
            $this->item_model->set_state('filter.owner', $owner_filter);
        } else {
            $this->item_model->unset_state('filter.owner');
        }

        //Prioridade
        $prioridade_post = $this->input->post('prioridade');
        $empresa_prioridade_filter = is_array($prioridade_post) ? $prioridade_post : [];
        if (!empty($empresa_prioridade_filter) && !in_array(-1, $empresa_prioridade_filter)) {
            $this->item_model->set_state('filter.prioridade', $empresa_prioridade_filter);
        } else {
            $this->item_model->unset_state('filter.prioridade');
        }

        //Pesquisar
        $search = $this->input->post('search') ? $this->input->post('search') : NULL;
        if (!is_null($search)) {
            $this->item_model->set_state("filter.search", $search);
            $data['search'] = $search;
        }else{
            $this->item_model->unset_state("filter.search");
            $data['search'] = NULL;
        }

        //Novo Material
        $novo_material_modal = $this->input->post('novo_material_modal') ? $this->input->post('novo_material_modal') : NULL;
        if(!empty($novo_material_modal) && $novo_material_modal != '-1'){
            $this->item_model->set_state('filter.novo_material_modal', $novo_material_modal);
        }else{
            $this->item_model->unset_state('filter.novo_material_modal');
        }

        //Estabelecimento
        $estabelecimento_modal = $this->input->post('estabelecimento_modal') ? $this->input->post('estabelecimento_modal') : NULL;
        if(is_array($estabelecimento_modal) && !in_array(-1, $estabelecimento_modal)){
            $this->item_model->set_state('filter.estabelecimento_modal', $estabelecimento_modal);
        }else{
            $this->item_model->unset_state('filter.estabelecimento_modal');
        }

        //NCM Proposto
        $ncm_proposta_modal = $this->input->post('ncm_proposta_modal') ? $this->input->post('ncm_proposta_modal') : NULL;
        if(is_array($ncm_proposta_modal) && !in_array(-1, $ncm_proposta_modal)){
            $this->item_model->set_state('filter.ncm_proposta_modal', $ncm_proposta_modal);
        }else{
            $this->item_model->unset_state('filter.ncm_proposta_modal');
        }

        //Sistema de Origem
        $sistema_origem_modal = $this->input->post('sistema_origem_modal') ? $this->input->post('sistema_origem_modal') : NULL;
        if(is_array($sistema_origem_modal) && !in_array(-1, $sistema_origem_modal)){
            $this->item_model->set_state('filter.sistema_origem_modal', $sistema_origem_modal);
        }else{
            $this->item_model->unset_state('filter.sistema_origem_modal');
        }

        //Ex-IPI
        $ex_ipi_modal = $this->input->post('ex_ipi_modal') ? $this->input->post('ex_ipi_modal') : NULL;
        if(is_array($ex_ipi_modal) && !in_array(-1, $ex_ipi_modal)){
            $this->item_model->set_state('filter.ex_ipi_modal', $ex_ipi_modal);
        }else{
            $this->item_model->unset_state('filter.ex_ipi_modal');
        }

        //Ex-II
        $ex_ii_modal = $this->input->post('ex_ii_modal') ? $this->input->post('ex_ii_modal') : NULL;
        if(is_array($ex_ii_modal) && !in_array(-1, $ex_ii_modal)){
            $this->item_model->set_state('filter.ex_ii_modal', $ex_ii_modal);
        }else{
            $this->item_model->unset_state('filter.ex_ii_modal');
        }

        //Descrição Completa
        $descricao_completa_modal = $this->input->post('descricao_completa_modal') ? $this->input->post('descricao_completa_modal') : NULL;
        if(!empty($descricao_completa_modal)){
            $this->item_model->set_state('filter.descricao_completa_modal', $descricao_completa_modal);
        }else{
            $this->item_model->unset_state('filter.descricao_completa_modal');
        }

        //Descrição Global
        $descricao_global_modal = $this->input->post('descricao_global_modal') ? $this->input->post('descricao_global_modal') : NULL;
        if(!empty($descricao_global_modal)){
            $this->item_model->set_state('filter.descricao_global_modal', $descricao_global_modal);
        }else{
            $this->item_model->unset_state('filter.descricao_global_modal');
        }

        //Data Inicial Modificação
        $data_inicio_modificacao_modal = $this->input->post('data_inicio_modificacao_modal') ? $this->input->post('data_inicio_modificacao_modal') : NULL;
        if (!is_null($data_inicio_modificacao_modal)) {
            $data_inicio_modificacao_modal = str_replace('/', '-', $data_inicio_modificacao_modal);
            $data_inicio_modificacao_modal = date('Y-m-d', strtotime($data_inicio_modificacao_modal));

            $this->item_model->set_state('filter.data_inicio_modificacao_modal', $data_inicio_modificacao_modal);
        }else{
            $this->item_model->unset_state('filter.data_inicio_modificacao_modal');
        }

        //Data Final Modificação
        $data_fim_modificacao_modal = $this->input->post('data_fim_modificacao_modal') ? $this->input->post('data_fim_modificacao_modal') : NULL;
        if (!is_null($data_fim_modificacao_modal)) {
            $data_fim_modificacao_modal = str_replace('/', '-', $data_fim_modificacao_modal);
            $data_fim_modificacao_modal = date('Y-m-d', strtotime($data_fim_modificacao_modal));

            $this->item_model->set_state('filter.data_fim_modificacao_modal', $data_fim_modificacao_modal);
        }else{
            $this->item_model->unset_state('filter.data_fim_modificacao_modal');
        }

        //Data Inicial Homologação
        $data_inicio_homologacao_modal = $this->input->post('data_inicio_homologacao_modal') ?
            $this->input->post('data_inicio_homologacao_modal') :
            null;
        if (!is_null($data_inicio_homologacao_modal)) {
            $data_inicio_homologacao_modal = str_replace('/', '-', $data_inicio_homologacao_modal);
            $data_inicio_homologacao_modal = date('Y-m-d', strtotime($data_inicio_homologacao_modal));

            $this->item_model->set_state('filter.data_inicio_homologacao_modal', $data_inicio_homologacao_modal);
        } else {
            $this->item_model->unset_state('filter.data_inicio_homologacao_modal');
        }

        //Data Final Homologação
        $data_fim_homologacao_modal = $this->input->post('data_fim_homologacao_modal') ?
            $this->input->post('data_fim_homologacao_modal') :
            null;
        if (!is_null($data_fim_homologacao_modal)) {
            $data_fim_homologacao_modal = str_replace('/', '-', $data_fim_homologacao_modal);
            $data_fim_homologacao_modal = date('Y-m-d', strtotime($data_fim_homologacao_modal));

            $this->item_model->set_state('filter.data_fim_homologacao_modal', $data_fim_homologacao_modal);
        } else {
            $this->item_model->unset_state('filter.data_fim_homologacao_modal');
        }
        //Data Inicial Homologação
        $data_inicio_importado_modal = $this->input->post('data_inicio_importado_modal') ?
        $this->input->post('data_inicio_importado_modal') : null;
        if (!is_null($data_inicio_importado_modal)) {
            $data_inicio_importado_modal = str_replace('/', '-', $data_inicio_importado_modal);
            $data_inicio_importado_modal = date('Y-m-d', strtotime($data_inicio_importado_modal));

            $this->item_model->set_state('filter.data_inicio_importado_modal', $data_inicio_importado_modal);
        } else {
            $this->item_model->unset_state('filter.data_inicio_importado_modal');
        }
        
        //Data Final Homologação
        $data_fim_importado_modal = $this->input->post('data_fim_importado_modal') ?
        $this->input->post('data_fim_importado_modal') : null;
        if (!is_null($data_fim_importado_modal)) {
            $data_fim_importado_modal = str_replace('/', '-', $data_fim_importado_modal);
            $data_fim_importado_modal = date('Y-m-d', strtotime($data_fim_importado_modal));

            $this->item_model->set_state('filter.data_fim_importado_modal', $data_fim_importado_modal);
        } else {
            $this->item_model->unset_state('filter.data_fim_importado_modal');
        }


        //Filtred
        if ($this->input->is_set('filtered')) {
            $this->item_model->set_state('filter.filtered', $this->input->post('filtered'));
        } else {
            $this->item_model->set_state('filter.filtered', 0);
        }

        //Outros
        $this->item_model->set_state('filter.ignore_like', TRUE);
        $empresa = $this->empresa_model->get_entry(sess_user_company());
        $data['campos_adicionais'] = explode("|", $empresa->campos_adicionais);

        $hasPnPrimarioSecundario = in_array('pn_primario_secundario', $data['campos_adicionais']);
        if($hasPnPrimarioSecundario){ 
            (!is_null($search)) ? $this->item_model->set_state("filter.pn_primario_secundario", TRUE): NULL;
        }else{
            $this->item_model->unset_state("filter.pn_primario_secundario");
        }

        $this->item_model->set_state('filter.id_empresa', sess_user_company());

        $itens = $this->item_model->get_entries_export_mestre_itens(true);
        
        $headerRow = array(
            array(
                'label' => 'PART-NUMBER',
                'field' => 'part_number',
                'col_format' => 'texto',
                'col_width' => 30,
                'data_type' => 'string'
            ),
            array(
                'label' => 'DESCRIÇÃO CURTA',
                'field' => 'descricao_curta',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'DESCRIÇÃO COMPLETA',
                'field' => 'descricao_completa',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'DESCRIÇÃO GLOBAL',
                'field' => 'descricao_global',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'ESTABELECIMENTO',
                'field' => 'estabelecimento',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'NCM PROPOSTO',
                'field' => 'ncm_proposto',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'EX – IPI',
                'field' => 'num_ex_ii',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'EX–II',
                'field' => 'num_ex_ipi',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'GRUPO TARIFÁRIO',
                'field' => 'descricao',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'OWNER',
                'field' => 'cod_owner',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'PRIORIDADE',
                'field' => 'empresa_prioridade',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'NOVO MATERIAL',
                'field' => 'integracao_novo_material',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'STATUS DO ITEM',
                'field' => 'status_formatado',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'EVENTO',
                'field' => 'evento',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'SISTEMA DE ORIGEM',
                'field' => 'sistema_origem',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'STATUS DE EXPORTAÇÃO',
                'field' => 'status_exportacao',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'DATA DE CRIAÇÃO',
                'field' => 'dat_criacao',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'DATA DE MODIFICAÇÃO',
                'field' => 'data_modificacao',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'IMPORTADO',
                'field' => 'importado',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'STATUS DE INTEGRAÇÃO DE ATRIBUTOS',
                'field' => 'status_integracao',
                'col_format' => 'texto',
                'col_width' => 30
            ),
            array(
                'label' => 'STATUS DE ATRIBUTOS',
                'field' => 'status_atributos',
                'col_format' => 'texto',
                'col_width' => 30
            ),
        );

        $funcoes_adicionais = explode('|', $empresa->funcoes_adicionais);
        if (in_array("integracao_ecomex", $funcoes_adicionais))
        {
            $headerRow = array_merge($headerRow, array(
                array(
                    'label' => 'DATA PREVISTA PO/INVOICE (COMEX)',
                    'field' => 'data_invoice',
                    'col_format' => 'texto',
                    'col_width' => 30
                )
            ));
        }
        
        $cabecalho = $this->extract_fields_from_cols($headerRow);

        $config = array(
            'filename'      => "mestre_itens_" . date('Y-m-d_H-i-s'),
            'titulo'        => "Mestre de Itens",
            'nome_planilha' => "Mestre de Itens",
            'colunas'       => $headerRow,
            'cabecalho'     => $cabecalho,
            'filter_suffix' => "HOM_"
        );

        require_once APPPATH . 'libraries/XlsxGenerator/XlsxGenerator.php';
        $relatorio = new XlsxGenerator($config['filename']);
        $writer = $relatorio->init($config['filename']);

        $relatorio->filename = $config['filename'];

        // Estrutura XLSX
        $widths = $relatorio->getWidth($config);
        $fields = array();

        if (isset($config['colunas'])) {
            foreach ($config['colunas'] as $coluna) {
                if (isset($coluna['label']))
                    $fields[$coluna['label']] = isset($coluna['col_format']) ? $coluna['col_format'] : 'string';
            }
        }

        $headerStyle = array(
            'font' => 'Arial',
            'font-size' => 12,
            'font-style' => 'bold',
            'color' => '#000000',
            'fill' => '#F9FF00',
            'halign' => 'center',
            'valign' => 'center',
            'border' => 'bottom',
            'wrap_text' => 'true',
            'widths' => $widths
        );

        $writer->writeSheetHeader($config['nome_planilha'], $fields, $headerStyle, TRUE);

        $defaultStyle = array(
            'font' => 'Arial',
            'font-size' => 11
        );

        $itemRows = array();
        
        $k = 0;
        $key = 0;

        $ncm_propostos = array();
        $num_ex_ii_list = array();
        $num_ex_ipi_list = array();
        $items_array = array();

        while ($item = $itens->unbuffered_row()) {
            $ncm_propostos[] = $item->ncm_proposto;
            if (!empty($item->num_ex_ii) && $item->num_ex_ii != '-1') {
                $num_ex_ii_list[] = $item->num_ex_ii;
            }
            if (!empty($item->num_ex_ipi) && $item->num_ex_ipi != '-1') {
                $num_ex_ipi_list[] = $item->num_ex_ipi;
            }
            $items_array[] = $item;
        }

        $ncm_propostos = array_filter(array_unique($ncm_propostos));
        $num_ex_ii_list = array_filter(array_unique($num_ex_ii_list));
        $num_ex_ipi_list = array_filter(array_unique($num_ex_ipi_list));
        
        $ex_tarifarios = array();
        
        if (!empty($ncm_propostos) && (!empty($num_ex_ii_list) || !empty($num_ex_ipi_list))) {

            $ex_tarifarios = $this->cad_item_model
                                ->get_all_ex_tarifarios(
                                    sess_user_company(),
                                    $ncm_propostos,
                                    $num_ex_ii_list,
                                    $num_ex_ipi_list
                                );
        }

        foreach ($items_array as $item) {
            $key++;
            $k = $key + 1;

            $dat_criacao = date("d/m/Y H:i:s", strtotime($item->dat_criacao));

            $data_modificacao = '';
            if (!empty($item->data_modificacao)) {
                $data_modificacao = date("d/m/Y H:i:s", strtotime($item->data_modificacao));
            }

            $integracao_novo_material  = ($item->integracao_novo_material  == 'S' ? 'SIM' : 'NÃO');

            $status_exportacao  = ($item->status_exportacao == '1' ? 'EXPORTADO' : 'PENDENTE');

            $owner_codigo = $item->owner_codigo ?? '';
            $owner_descricao = $item->owner_descricao ?? '';
            $responsavel_owner = $item->responsaveis_gestores_nomes ?? '';

            $num_ex_ii = '';
            $num_ex_ipi = '';

            if (!empty($item->num_ex_ii) && $item->num_ex_ii != '-1') {
                // Remove todos os tipos de espaços em branco e normaliza a string
                $num_ex_ii_normalized = preg_replace('/\s+/', '', $item->num_ex_ii);
                
                $key_ii = sprintf('%s_%s_1', $item->ncm_proposto, $num_ex_ii_normalized);
                $num_ex_ii = sprintf('%s - %s', $num_ex_ii_normalized,
                    $ex_tarifarios[$key_ii] ?? '');

            } elseif ($item->num_ex_ii == '-1') {
                $num_ex_ii = "ITEM NÃO SE ENQUADRA";
            }

            if (!empty($item->num_ex_ipi) && $item->num_ex_ipi != '-1') {
                // Remove todos os tipos de espaços em branco e normaliza a string
                $num_ex_ipi_normalized = preg_replace('/\s+/', '', $item->num_ex_ipi);
                
                $key_ipi = sprintf('%s_%s_6', $item->ncm_proposto, $num_ex_ipi_normalized);
                $num_ex_ipi = sprintf('%s - %s', $num_ex_ipi_normalized,
                    $ex_tarifarios[$key_ipi] ?? '');

            } elseif ($item->num_ex_ipi == '-1') {
                $num_ex_ipi = "ITEM NÃO SE ENQUADRA";
            }
 
            $status_integracao_table = [
                1 => 'Pendente de integração',
                2 => 'Enviado ao sistema de COMEX',
                3 => 'Integrado com PUCOMEX com sucesso'
            ];

            if (!empty($item->status_integracao))
            {
                $status_integracao = isset($status_integracao_table[$item->status_integracao]) ? $status_integracao_table[$item->status_integracao] : '';
            } else {
                $status_integracao = 'Pendente de integração';
            }

            $status_wf_atributos_table = [
                1 => 'Item nacional',
                2 => 'Análise de atributos - Fiscal',
                3 => 'Preenchimento/Validação Engenharia',
                4 => 'Homologação da Classificação Fiscal',
                5 => 'Em revisão',
                6 => 'Em revisão por alteração no PUCOMEX',
                7 => 'Homologados'
            ];

            if (!empty($item->status_atributos))
            {
                $status_atributos = isset($status_wf_atributos_table[$item->status_atributos]) ? $status_wf_atributos_table[$item->status_atributos] : '';
            } else {
                $status_atributos = 'Não possui status de atributos';
            }

            $format_date_invoice = '';
            if (!empty($item->data_invoice))
            {
                $data_invoice = strtotime($item->data_invoice);
                $format_date_invoice = date('d/m/Y', $data_invoice);
            }

            $itemRows[$k] = array(
                    $item->part_number,
                    $item->descricao_curta,
                    $item->descricao_completa,
                    $item->descricao_global,
                    $item->estabelecimento,
                    $item->ncm_proposto,
                    $num_ex_ipi,
                    $num_ex_ii,
                    $item->descricao,
                    $owner_codigo . ' - ' . $owner_descricao . ' - ' . $responsavel_owner,
                    $item->empresa_prioridade,
                    $integracao_novo_material,
                    $item->status_formatado,
                    $item->evento,
                    $item->sistema_origem,
                    $status_exportacao,
                    $dat_criacao,
                    $data_modificacao,
                    $item->importado ? 'Importado' : 'Não Importado',
                    $status_integracao,
                    $status_atributos
            );

            if (in_array("integracao_ecomex", $funcoes_adicionais))
            {
                $itemRows[$k] = array_merge($itemRows[$k], array(
                    $format_date_invoice
                ));
            } 
        }

        $k = 2;
        foreach($itemRows as $item) {
            $row = array();
            foreach($headerRow as $i => $value) {
                $row += array($headerRow[$i]['field'] => $itemRows[$k][$i]);
            }
            $k++;
            $writer->writeSheetRow($config['nome_planilha'], $row, $defaultStyle);
           
        }

        return $relatorio->download();
    }

    private function extract_fields_from_cols($cols, $line_break = ' ', $label_key_override = 'label_exportar') {
        if (empty($cols)) {
            return array();
        }

        $fields = array();

        foreach ($cols as $col) {
            $label_key = 'label';

            if (!empty($label_key_override) && array_key_exists($label_key_override, $col)) {
                $label_key = $label_key_override;
            }

            if (!empty($line_break)) {
                $col[$label_key] = str_replace('<br>', $line_break, $col[$label_key]);
            }

            $fields[$col['field']] = $col[$label_key];
        }

        return $fields;
    }
}
