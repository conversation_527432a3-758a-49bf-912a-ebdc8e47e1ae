#!/bin/bash
# Script para testar a API DIANA usando curl
# Uso:
#   ./test_api_curl.sh [quick|full] [arquivo1.(png|jpg|jpeg|pdf)] [arquivo2...] 
# Se nenhum arquivo for informado no modo full, será gerado um PNG 1x1 temporário.

set -euo pipefail

API_ENDPOINT="http://*************/gt/perguntas"
ALLOWED_EXTS="png|jpg|jpeg|pdf"
TIMEOUT=30

echo "🚀 Testando API DIANA"
echo "========================================"

# Utilidades ---------------------------------------------------------------
error() { echo "❌ $*" >&2; }
has_cmd() { command -v "$1" >/dev/null 2>&1; }

# Retorna extensão do arquivo em minúsculas
file_ext() { local f="$1"; echo "${f##*.}" | tr '[:upper:]' '[:lower:]'; }

# Gera um PNG 1x1 em /tmp e retorna o caminho
create_sample_png() {
    local out="/tmp/diana_sample_$$.png"
    # 1x1 transparente PNG
    base64 -d > "$out" <<'B64'
iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/x8AAusB9YqQ9h8AAAAASUVORK5CYII=
B64
    echo "$out"
}

# Converte arquivo para base64 (sem quebras)
file_to_base64() { base64 -w 0 "$1"; }

# Monta JSON de documentos a partir de caminhos de arquivo passados como parâmetros
# Saída: string JSON tipo: [{"nome":"...","conteudo_base64":"..."}, ...]
build_docs_json() {
    local docs=()
    local f name ext b64
    for f in "$@"; do
        if [ ! -f "$f" ]; then
            error "Arquivo não encontrado: $f"; exit 1
        fi
        ext=$(file_ext "$f")
        if ! [[ "$ext" =~ ^(${ALLOWED_EXTS})$ ]]; then
            error "Extensão não permitida: .$ext (permitidas: ${ALLOWED_EXTS//|/, })"; exit 1
        fi
        name=$(basename "$f")
        b64=$(file_to_base64 "$f")
        docs+=("{\"nome\":\"$name\",\"conteudo_base64\":\"$b64\"}")
    done
    local IFS=","; echo "[${docs[*]}]"
}

# Função para teste de conectividade
test_connectivity() {
    echo "🔍 Testando conectividade..."
    
    # Verificar se o endpoint está acessível
    status_code=$(curl -s -o /dev/null -w "%{http_code}" -X OPTIONS "$API_ENDPOINT")
    
    if [ "$status_code" -eq 200 ] || [ "$status_code" -eq 405 ]; then
        echo "✅ Endpoint acessível - Status: $status_code"
        
        # Verificar métodos permitidos
        allow_header=$(curl -s -I -X OPTIONS "$API_ENDPOINT" | grep -i "allow:" | cut -d' ' -f2- | tr -d '\r')
        if [ -n "$allow_header" ]; then
            echo "📋 Métodos permitidos: $allow_header"
        fi
        return 0
    else
        echo "❌ Endpoint não acessível - Status: $status_code"
        return 1
    fi
}

# Função para teste com payload mínimo
test_minimal() {
    echo ""
    echo "🔍 Teste com payload mínimo..."
    
    payload='{
        "documentos_upload": [],
        "perguntas": [
            {
                "id": 1,
                "pergunta": "Qual é o NCM correto para este produto?"
            }
        ],
        "contexto": {
            "cliente_segmento": "Industrial",
            "produto_descricao": "Produto de teste",
            "ncm_atual": "12345678",
            "ncm_fornecedor": "87654321"
        }
    }'
    
    echo "📤 Enviando requisição..."
    echo "📋 Payload:"
    echo "$payload" | jq '.' 2>/dev/null || echo "$payload"
    
    start_time=$(date +%s.%N)
    response=$(curl -s -m "$TIMEOUT" -w "\nHTTP_CODE:%{http_code}\nTIME_TOTAL:%{time_total}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$payload" \
        "$API_ENDPOINT")
    end_time=$(date +%s.%N)
    
    # Extrair informações da resposta
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d':' -f2)
    time_total=$(echo "$response" | grep "TIME_TOTAL:" | cut -d':' -f2)
    json_response=$(echo "$response" | sed '/HTTP_CODE:/,$d')
    
    echo "⏱️  Tempo de resposta: ${time_total}s"
    echo "📥 Status HTTP: $http_code"
    echo "📋 Resposta:"
    echo "$json_response" | jq '.' 2>/dev/null || echo "$json_response"
    
    if [ "$http_code" = "200" ]; then
        echo "✅ Sucesso!"
        return 0
    elif [ "$http_code" = "422" ]; then
        echo "⚠️  Erro de validação (pode ser esperado)"
        return 0
    else
        echo "❌ Erro inesperado"
        return 1
    fi
}

# Função para teste completo
test_complete() {
    echo ""
    echo "🔍 Teste com payload completo (com arquivo(s))..."

    # Coleta arquivos da linha de comando (após o primeiro argumento)
    shift || true
    local files=("$@")

    # Se nenhum arquivo informado, gera um PNG 1x1 temporário
    if [ ${#files[@]} -eq 0 ]; then
        local sample
        sample=$(create_sample_png)
        files=("$sample")
        echo "📎 Nenhum arquivo informado. Usando amostra: $sample"
    fi

    local docs_json
    docs_json=$(build_docs_json "${files[@]}")

    payload='{
        "documentos_upload": __DOCS__,
        "perguntas": [
            { "id": 1, "pergunta": "Qual é o NCM correto para este produto?" },
            { "id": 2, "pergunta": "Há algum regime especial aplicável?" }
        ],
        "contexto": {
            "cliente_segmento": "Industrial",
            "produto_descricao": "Equipamento eletrônico para uso industrial",
            "ncm_atual": "85444290",
            "ncm_fornecedor": "85444200"
        }
    }'

    payload=${payload/__DOCS__/$docs_json}

    echo "📤 Enviando requisição completa..."

    start_time=$(date +%s.%N)
    response=$(curl -s -m "$TIMEOUT" -w "\nHTTP_CODE:%{http_code}\nTIME_TOTAL:%{time_total}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$payload" \
        "$API_ENDPOINT")
    end_time=$(date +%s.%N)

    # Extrair informações da resposta
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d':' -f2)
    time_total=$(echo "$response" | grep "TIME_TOTAL:" | cut -d':' -f2)
    json_response=$(echo "$response" | sed '/HTTP_CODE:/,$d')

    echo "⏱️  Tempo de resposta: ${time_total}s"
    echo "📥 Status HTTP: $http_code"
    echo "📋 Resposta:"
    echo "$json_response" | jq '.' 2>/dev/null || echo "$json_response"

    if [ "$http_code" = "200" ]; then
        echo "✅ Sucesso com payload completo!"
        return 0
    elif [ "$http_code" = "422" ]; then
        echo "⚠️  Erro de validação"
        return 0
    else
        echo "❌ Erro inesperado"
        return 1
    fi
}

# Função para teste de validação
test_validation() {
    echo ""
    echo "🔍 Teste de validação (payload inválido)..."
    
    payload='{
        "perguntas": [
            {
                "id": "invalid_id",
                "pergunta": ""
            }
        ],
        "contexto": {
            "cliente_segmento": "",
            "ncm_atual": "invalid_ncm"
        }
    }'
    
    echo "📤 Enviando payload inválido para testar validação..."
    
    response=$(curl -s -w "\nHTTP_CODE:%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$payload" \
        "$API_ENDPOINT")
    
    http_code=$(echo "$response" | grep "HTTP_CODE:" | cut -d':' -f2)
    json_response=$(echo "$response" | sed '/HTTP_CODE:/,$d')
    
    echo "📥 Status HTTP: $http_code"
    echo "📋 Resposta:"
    echo "$json_response" | jq '.' 2>/dev/null || echo "$json_response"
    
    if [ "$http_code" = "422" ]; then
        echo "✅ Validação funcionando corretamente!"
        return 0
    else
        echo "⚠️  Resposta inesperada para payload inválido"
        return 1
    fi
}

# Função principal
main() {
    local test_type=${1:-"quick"}
    
    # Verificar se curl está disponível
    if ! command -v curl &> /dev/null; then
        echo "❌ curl não encontrado. Instale o curl para usar este script."
        exit 1
    fi
    
    # Teste de conectividade sempre é executado
    if ! test_connectivity; then
        echo "❌ Falha na conectividade. Abortando testes."
        exit 1
    fi
    
    success_count=0
    total_tests=0
    
    if [ "$test_type" = "quick" ]; then
        echo ""
        echo "⚡ Executando teste rápido..."
        total_tests=1
        test_minimal && ((success_count++))
    else
        echo ""
        echo "🔄 Executando teste completo..."
        total_tests=3

        # Passa todos os argumentos após o primeiro (tipo) como arquivos
        shift || true

        test_minimal && ((success_count++))
        echo "----------------------------------------"

        test_complete "$@" && ((success_count++))
        echo "----------------------------------------"

        test_validation && ((success_count++))
    fi
    
    # Resumo
    echo ""
    echo "========================================"
    echo "📊 RESUMO DOS TESTES"
    echo "========================================"
    echo "Sucessos: $success_count/$total_tests"
    
    if [ $success_count -gt 0 ]; then
        echo "💡 A API está funcionando e pronta para receber requisições!"
        exit 0
    else
        echo "⚠️  A API pode precisar de ajustes."
        exit 1
    fi
}

# Executar função principal com argumentos
main "$@"