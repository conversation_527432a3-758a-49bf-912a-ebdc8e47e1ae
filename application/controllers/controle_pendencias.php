<?php if (! defined('BASEPATH')) exit('No direct script access allowed');

/**
 * Controle de pendências (perguntas)
 * @property MY_Input $input
 * @property CI_Loader $load
 * @property CI_Output $output
 * @property CI_DB_query_builder $db
 * @property CI_Session $session
 * @property Breadcrumbs $breadcrumbs
 * @property Pagination $pagination
 * @property MY_Config $config
 * @property Excel $excel
 * @property Unzip $unzip
 * @property Email $email
 * @property CI_upload $upload
 * @property Status $status
 * Models carregadas dinamicamente
 * @property Ctr_pendencias_pergunta_model $ctr_pendencias_pergunta_model
 * @property Ctr_grupo_pergunta_model $ctr_grupo_pergunta_model
 * @property Ctr_resposta_model $ctr_resposta_model
 * @property Item_model $item_model
 * @property Item_log_model $item_log_model
 * @property Cad_item_model $cad_item_model
 * @property Empresa_model $empresa_model
 * @property Usuario_model $usuario_model
 * @property Ctr_pendencias_item_model $ctr_pendencias_item_model
 * @property Ctr_pendencias_resposta_model $ctr_pendencias_resposta_model
 * @property Empresa_prioridades_model $empresa_prioridades_model
 */
class Controle_pendencias extends MY_Controller
{
    public $title = "Controle de Pendências";

    public function __construct()
    {
        parent::__construct();

        if (!is_logged()) {
            redirect('/login');
        }

        if (has_role('becomex_pmo') && !has_role('sysadmin')) {
            $this->ctr_pendencias_pergunta_model->set_state('get_from_all_owners', TRUE);
        } else {
            $this->ctr_pendencias_pergunta_model->set_state('get_from_all_owners', FALSE);
        }


        $this->load->library('breadcrumbs');
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Controle de pendências', '/controle_pendencias/');
    }

    private function apply_default_filter()
    {
        $user_id = sess_user_id();
        if ($unsetFilters = $this->input->is_set("unsetFilters")) {
            $this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
            $this->ctr_pendencias_pergunta_model->clear_states();
            $this->ctr_pendencias_pergunta_model->unset_state("filter.bulk_item");
            $this->ctr_pendencias_pergunta_model->unset_state("filter.bulk_state");
            $this->ctr_pendencias_pergunta_model->unset_state('filter.usuarioPergunta');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.partnumbers');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.periodoIni');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.periodoFim');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.status');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.ownerPergunta');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.prioridade');
        } else {
            $this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
            $this->ctr_pendencias_pergunta_model->restore_state_from_session('filter.', 'get');
        }

        if ($this->input->is_set('partnumbers')) {
            $partnumbers = $this->input->get('partnumbers');
            $this->ctr_pendencias_pergunta_model->set_state('filter.partnumbers', $partnumbers);
        } else {
            $this->ctr_pendencias_pergunta_model->unset_state('filter.partnumbers');
        }

        if ($this->input->is_set('usuarioPergunta')) {
            $usuarioPergunta = $this->input->get('usuarioPergunta');
            $this->ctr_pendencias_pergunta_model->set_state('filter.usuarioPergunta', $usuarioPergunta);
            $this->ctr_pendencias_pergunta_model->unset_state('filter.ownerPergunta');
        }

        if ($this->input->is_set('ownerPergunta')) {
            $ownerPergunta = $this->input->get('ownerPergunta');
            $this->ctr_pendencias_pergunta_model->set_state('filter.ownerPergunta', $ownerPergunta);
            $this->ctr_pendencias_pergunta_model->unset_state('filter.usuarioPergunta');
        }

        // if (empty($this->ctr_pendencias_pergunta_model->get_state('filter.usuarioPergunta'))){
        //     $this->ctr_pendencias_pergunta_model->set_state('filter.usuarioPergunta', array($user_id));
        // }

        if ($this->input->is_set('periodoIni')) {
            $periodoIni = $this->input->get('periodoIni');
            $this->ctr_pendencias_pergunta_model->set_state('filter.periodoIni', $periodoIni);
        }


        if ($this->input->is_set('periodoFim')) {
            $periodoFim = $this->input->get('periodoFim');
            $this->ctr_pendencias_pergunta_model->set_state('filter.periodoFim', $periodoFim);
        }

        if ($this->input->is_set('status')) {
            $status = $this->input->get('status');
            $this->ctr_pendencias_pergunta_model->set_state('filter.status', $status);
        }

        if ($this->input->is_set('prioridade')) {
            $prioridade = $this->input->get('prioridade');
            $this->ctr_pendencias_pergunta_model->set_state('filter.prioridade', $prioridade);
        }

        // if ($this->input->is_set('search_questions')) {
        //     $pesquisar = $this->input->get('search_questions');
        //     $this->ctr_pendencias_pergunta_model->set_state('filter.search_questions', $pesquisar);
        // } else {
        //     $this->ctr_pendencias_pergunta_model->unset_state('filter.search_questions');
        // }
    }

    public function exportar()
    {
        $this->load->model('ctr_pendencias_pergunta_model');

        $this->apply_default_filter();

        if ($bulk_item = $this->ctr_pendencias_pergunta_model->get_state('filter.bulk_item')) {
            $bulk_item = array_map(function ($item) {
                return explode('&', $item)[0];
            }, $bulk_item);

            $itens_selected = implode(' ', $bulk_item);
        } else {
            $itens_selected = null;
        }

        $partNumbersPerguntas = $this->ctr_pendencias_pergunta_model
            ->getPartnumbersComPerguntasPendentes(
                null,
                null,
                false,
                true,
                false,
                false,
                null,
                $itens_selected
            );

        if (empty($partNumbersPerguntas))
            redirect('controle_pendencias');

        $descricoes = [];
        $evento = [];
        foreach ($partNumbersPerguntas as $p) {
            $item = ['part_number' => $p->part_number, 'estabelecimento' => $p->estabelecimento, 'id_empresa' => $p->id_empresa];
            $perguntas_item = $this->ctr_pendencias_pergunta_model->getPerguntas($item);

            if (empty($perguntas_item))
                continue;

            $estabelecimento = empty($p->estabelecimento) ? 'N/A' : $p->estabelecimento;
            $perguntas[$p->part_number][$estabelecimento] = $perguntas_item;
            $descricoes[$p->part_number][$estabelecimento] = $p->descricao;
            $evento[$p->part_number][$estabelecimento] = $p->evento;
        }

        $itens = [];
        $k = 0;
        $count_block = 1;

        foreach ($perguntas as $part_number => $item) {

            foreach ($item as $estabelecimento => $perguntas) {
                foreach ($perguntas as $pergunta) {
                    if (empty($perguntas))
                        continue;

                    $usuarioResponsavel = $pergunta->nome_responsavel ?
                        $pergunta->nome_responsavel . ' - ' . $pergunta->email_responsavel :
                        ' - ';

                    $block[] = $count_block;
                    $itens[$k] = array(
                        $part_number,
                        $descricoes[$part_number][$estabelecimento],
                        $estabelecimento,
                        $evento[$part_number][$estabelecimento],
                        $usuarioResponsavel,
                        'Pergunta: ' . $pergunta->pergunta,
                        ' '
                    );
                    $k++;
                    $count_block++;
                }
            }
        }

        $this->load->library('Excel');

        $this->excel->setActiveSheetIndex(0);
        $this->excel->getActiveSheet()->setTitle('Log - Atribuição de grupos');
        $this->excel->getActiveSheet()->setCellValue('A1', 'PART-NUMBER');
        $this->excel->getActiveSheet()->setCellValue('B1', 'DESCRICAO');
        $this->excel->getActiveSheet()->setCellValue('C1', 'ESTABELECIMENTO');
        $this->excel->getActiveSheet()->setCellValue('D1', 'EVENTO/PACOTE');
        $this->excel->getActiveSheet()->setCellValue('E1', 'RESPONSÁVEL');
        $this->excel->getActiveSheet()->setCellValue('F1', 'PERGUNTAS');
        $this->excel->getActiveSheet()->setCellValue('G1', 'RESPOSTAS');

        $this->excel->getActiveSheet()->getStyle('A1:G1')->getFont()->setBold(true);
        $this->excel->getActiveSheet()->getStyle('A1:G1')->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()->setARGB('F9FF00');

        $this->excel->getActiveSheet()->getStyle('A1:G1')->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);

        $this->excel->getActiveSheet()->getColumnDimension('A')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('B')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('C')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('D')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('E')->setWidth('80');
        $this->excel->getActiveSheet()->getColumnDimension('F')->setWidth('30');
        $this->excel->getActiveSheet()->getColumnDimension('G')->setWidth('30');

        if (!empty($perguntas)) {
            $horizontal_left = array(
                'alignment' => array(
                    'horizontal' => PHPExcel_Style_Alignment::HORIZONTAL_LEFT,
                ),
            );

            $colunas = [0 => 'A', 1 => 'B', 2 => 'C', 3 => 'D', 4 => 'E', 5 => 'F', 6 => 'G'];
            $count = 1;
            foreach ($itens as $k => $data_item) {
                $count++;
                foreach ($colunas as $y => $coluna) {
                    $this->excel->getActiveSheet()->setCellValueExplicit($coluna . $count, $data_item[$y], PHPExcel_Cell_DataType::TYPE_STRING);
                }
            }


            $filename = 'perguntas_pendentes_' . date('Y-m-d_H-i-s') . '.xlsx';

            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            $objWriter = PHPExcel_IOFactory::createWriter($this->excel, 'Excel2007');
            $objWriter->save('php://output');
        }
    }

    public function bulk_clean()
    {
        $this->load->model('ctr_pendencias_pergunta_model');

        $this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
        $this->ctr_pendencias_pergunta_model->unset_state("filter.bulk_item");
        return true;
    }

    public function bulk()
    {
        $this->load->model('ctr_pendencias_pergunta_model');
        $this->ctr_pendencias_pergunta_model->set_state("filter.bulk_state", TRUE);
        $this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);

        $estabelecimento =  $this->input->post('estabelecimento');
        $part_number =  $this->input->post('part_number');
        $item_marcado =  $this->input->post('checked');

        $result = $this->ctr_pendencias_pergunta_model->getBulk();

        if (!empty($result)) {
            $bulkItem = $result['item'];

            if ($item_marcado == 0) {

                $item = $part_number . '&' . $estabelecimento;
                $key = array_search($item, $bulkItem);

                unset($bulkItem[$key]);
            } else {

                array_push($bulkItem, $part_number . '&' . $estabelecimento);
                $bulkItem = array_unique($bulkItem);
            }
        } else {

            $bulkItem = array($part_number . '&' . $estabelecimento);
        }

        if (empty($bulkItem)) {
            $this->ctr_pendencias_pergunta_model->unset_state("filter.bulk_item");

            return response_json(array(
                "item_result" => null
            ));
        }

        $this->ctr_pendencias_pergunta_model->set_state("filter.bulk_item", $bulkItem);

        $result = $this->ctr_pendencias_pergunta_model->getBulk();
        $item_result = $result['item'];

        return response_json(array(
            "item_result" => $item_result
        ));
    }

    public function index()
    {
        $user_id = sess_user_id();
        $this->load->model('ctr_pendencias_pergunta_model');
        $this->load->model('cad_item_model');
        $this->load->model('item_model');

        //Desbloqueia todos os itens do usuário da sessão
        $this->item_model->unset_usuario_bloqueador_itens(sess_user_company());

        $this->title = "Controle de Pendências";

        if ($this->input->post('baixar') == 1) {
            return $this->exportar();
        }

        set_time_limit(0);

        $data = array();

        $this->load->helper('formatador_helper');
        if ($this->input->post('send') == 1) {
            $id_empresa = sess_user_company();

            $upload_path = config_item('upload_tmp_path');

            $config['upload_path'] = $upload_path;
            $config['allowed_types'] = 'xlsx';
            $config['max_size'] = 2147483648;

            $this->load->library('unzip');
            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('arquivo')) {
                $err = "<h4>Ops... alguns erros aconteceram</h4><ul>" . $this->upload->display_errors('<li>', '</li>') . "</ul>";
                $this->message_on_render($err, 'error');
            } else {

                $upload_data = $this->upload->data();

                $file_ext = strtolower($upload_data['file_ext']);
                if ($file_ext == '.xlsx') {

                    include APPPATH . 'libraries/xlsxreader.php';

                    $xlsx = new XLSXReader($upload_data['full_path']);
                    $sheetNames = $xlsx->getSheetNames();
                    $sheetActive = current($sheetNames);
                    $sheet = $xlsx->getSheet($sheetActive);

                    $this->load->helper('text');

                    $log['atualizado'] = 0;
                    $log['erro'] = 0;
                    $count = 0;
                    foreach ($sheet->getData() as $rows) {
                        $count++;
                        if ($count > 1) {
                            $part_number =  $rows[0];
                            $estabelecimento = trim($rows[2]);
                            $pergunta =  str_replace("Pergunta:", " ", $rows[5]);
                            $pergunta = trim(convert_accented_characters($pergunta));
                            $resposta = isset($rows[6]) ? trim(convert_accented_characters($rows[6])) : null;
                        } else {
                            continue;
                        }

                        if (empty($resposta))
                            continue;

                        $respostas_item = [];

                        if ($pergunta == 'Informar o resumo do item.') {
                            $respostas_item['descricao_proposta_completa'] = $resposta;
                        }
                        if ($pergunta == 'Informe as observacoes do item.' || $pergunta ==  'Informe as observações do item.') {
                            $respostas_item['observacoes'] = $resposta;
                        }

                        $estabelecimento = $estabelecimento == 'N/A' || $estabelecimento == '' ? null : $estabelecimento;

                        if (!empty($respostas_item)) {
                            $this->item_model->update_item($part_number, $id_empresa, $respostas_item, FALSE, $estabelecimento);
                        }

                        $result = $this->ctr_pendencias_pergunta_model->responderPerguntasLote($part_number, $estabelecimento, $pergunta, $resposta);
                        // $log['debug'][] = $part_number." estab: ".$estabelecimento." pergunta: ".$pergunta." resp: ".$resposta." result: ".$result;
                        //rawurlencode($part_number)

                        $check_perguntas = $this->ctr_pendencias_pergunta_model->getPerguntasByPn($part_number, $estabelecimento);

                        try {
                            $check_cad_item = $this->item_model->get_entry($part_number, $id_empresa, $estabelecimento);
                        } catch (Exception $e) {
                            $this->message_next_render("Falha na atualização " . $e->getMessage() . "&nbsp; &nbsp;" . $part_number . "&nbsp; &nbsp;" . $estabelecimento, 'error');
                            redirect('controle_pendencias');
                        }

                        $ids_status_tela_homologacao = ['1', '2', '3', '4', '5', '10'];
                        $check_status_item_homologacao = (in_array($check_cad_item->id_status, $ids_status_tela_homologacao));

                        if (
                            isset($check_perguntas)
                            && !empty($check_perguntas)
                            && $check_perguntas['possui_perguntas'] == true
                            && $check_perguntas['pergunta_pendente'] == false
                            && !empty($check_cad_item)
                            && !$check_status_item_homologacao
                        ) {
                            $this->load->library("Item/Status");
                            $this->status->set_status("respondido");
                            $this->status->update_item($part_number, $estabelecimento, $id_empresa);
                        }

                        if ($result == true) {
                            $log['atualizado']++;
                        } else {
                            $log['erro']++;
                        }
                    }

                    if ($log['atualizado'] > 0) {
                        $msg = $log['atualizado'] == 1 ? 'Uma Resposta foi Atualizada' : $log['atualizado'] . ' Respostas foram Atualizadas';
                        $success = "<h4>Importação Realizada com Sucesso</h4> <p> " . $msg . " </p><ul>" . $this->upload->display_errors('<li>', '</li>') . "</ul>";
                        $this->message_on_render($success, 'success');
                    }
                }
            }
        }

        $this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);
        if ($this->input->get('search_questions')) {
            $this->ctr_pendencias_pergunta_model->unset_state("filter.bulk_item");
            $this->ctr_pendencias_pergunta_model->unset_state("filter.bulk_state");
            $this->ctr_pendencias_pergunta_model->unset_state('filter.usuarioPergunta');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.partnumbers');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.periodoIni');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.periodoFim');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.status');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.ownerPergunta');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.prioridade');
            $this->ctr_pendencias_pergunta_model->unset_state('filter.responsible_type');
        }

        $this->breadcrumbs->push('Perguntas pendentes', '/controle_pendencias/');

        $this->load->library('pagination');
        $this->load->model([
            'ctr_pendencias_pergunta_model',
            'usuario_model',
            'empresa_model',
            'item_model',
            'empresa_prioridades_model',
        ]);

        $per_page = $this->input->get('per_page');

        if ($this->input->get('responsible_type')) {
            $this->ctr_pendencias_pergunta_model->set_state('filter.responsible_type', $this->input->get('responsible_type'));
        }
        $responsible_type =  $this->ctr_pendencias_pergunta_model->get_state('filter.responsible_type');
        $search_part_number = $this->input->get('partnumbers');
        $limit = 20;
        $offset = $per_page;

        $this->apply_default_filter();

        $empresa = $this->empresa_model->get_entry(sess_user_company());

        $campos_adicionais = explode("|", $empresa->campos_adicionais);
        $hasOwner = in_array('owner', $campos_adicionais);
        $hasPrioridade = in_array('prioridade', $campos_adicionais);

        $partNumbersFilter = $this->ctr_pendencias_pergunta_model->getPartnumbersComPerguntasPendentesFilter();
        $usuariosFilter = $this->ctr_pendencias_pergunta_model->getUsuariosResponsaveisComPerguntasRealizadas();
        $usuariosFilterAtivos = array_values(array_filter($usuariosFilter, function ($usuario) {
            return $usuario->status == 1;
        }));
        $usuariosFilterInativos = array_values(array_filter($usuariosFilter, function ($usuario) {
            return $usuario->status == 0;
        }));

        $owner_user = $this->item_model->get_user_owner_codes(sess_user_id());

        if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
            $ownersFilter = $this->empresa_model->get_owners_by_empresa($empresa, $owner_user);
        } else {
            $ownersFilter = $this->empresa_model->get_owners_by_empresa($empresa);
        }

        $ownersToTransfer = $this->empresa_model->get_owners_by_empresa($empresa);

        $id_usuario = sess_user_id();

        $remover_busca_estabelecimento = true;

        if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
            $partNumbersPerguntas = $this->ctr_pendencias_pergunta_model->getPartnumbersComPerguntasPendentes($limit, $offset, false, $search_part_number, $remover_busca_estabelecimento, false, $owner_user);
        } else {
            $partNumbersPerguntas = $this->ctr_pendencias_pergunta_model->getPartnumbersComPerguntasPendentes($limit, $offset, false, $search_part_number, $remover_busca_estabelecimento);
        }

        if (!customer_has_role('editar_todos_part_numbers', sess_user_id()) && $hasOwner) {
            $total = $this->ctr_pendencias_pergunta_model->getPartnumbersComPerguntasPendentes(null, null, true, $search_part_number, $remover_busca_estabelecimento, false, $owner_user);
        } else {
            $total = $this->ctr_pendencias_pergunta_model->getPartnumbersComPerguntasPendentes(null, null, true, $search_part_number, $remover_busca_estabelecimento);
        }

        $this->usuario_model->set_state('filter.id_empresa', sess_user_company());
        $this->usuario_model->set_state('filter.ocultar_inativos', 1);
        $responsaveis_ativos = $this->usuario_model->get_entries();

        $ids_usuarios = array_map(function ($usuario) {
            return $usuario->id_usuario;
        }, $usuariosFilter);

        if (in_array($user_id, $ids_usuarios)) {
            $usuario_default_filtro =  [$user_id];
        } else {
            $usuario_default_filtro = $ids_usuarios;
        }


        $usuarios        = !empty($this->ctr_pendencias_pergunta_model->get_state('filter.usuarioPergunta')) ? $this->ctr_pendencias_pergunta_model->get_state('filter.usuarioPergunta') : $usuario_default_filtro;

        $owners = !empty($this->ctr_pendencias_pergunta_model->get_state('filter.ownerPergunta')) ? $this->ctr_pendencias_pergunta_model->get_state('filter.ownerPergunta') : array();

        $partnumbers        = !empty($this->ctr_pendencias_pergunta_model->get_state('filter.partnumbers'))     ? $this->ctr_pendencias_pergunta_model->get_state('filter.partnumbers')     : null;
        $periodo_inicio     = !empty($this->ctr_pendencias_pergunta_model->get_state('filter.periodoIni'))      ? $this->ctr_pendencias_pergunta_model->get_state('filter.periodoIni')      : null;
        $periodo_fim        = !empty($this->ctr_pendencias_pergunta_model->get_state('filter.periodoFim'))      ? $this->ctr_pendencias_pergunta_model->get_state('filter.periodoFim')      : null;

        // Garantir que $status sempre seja um array válido para evitar warnings no in_array()
        $status_state = $this->ctr_pendencias_pergunta_model->get_state('filter.status');
        $status = (!empty($status_state) && is_array($status_state)) ? $status_state : array();

        $prioridades        = !empty($this->ctr_pendencias_pergunta_model->get_state('filter.prioridade'))     ? $this->ctr_pendencias_pergunta_model->get_state('filter.prioridade')     : array();

        $prioridadesFilter =  $this->empresa_prioridades_model->get_entry($empresa->id_empresa);

        $bulk = $this->ctr_pendencias_pergunta_model->getBulk();
        $pesquisa = $this->input->get('partnumbers') ? $this->input->get('partnumbers') : '';
        $itens_pn = [];
        if (is_array($pesquisa)) {
            foreach ($pesquisa as $p) {
                $part_number_array = explode("&", $p);
                $itens_pn[] =  $part_number_array[0];
            }
        }
        if (!empty($itens_pn)) {
            $separator = get_company_separator(null);
            $pesquisa = implode($separator, $itens_pn);
        }

        $pesquisa = is_array($pesquisa) ? reset($pesquisa) : $pesquisa;
        $this->pagination->initialize(array(
            'base_url' => 'controle_pendencias',
            'uri_segment' => 3,
            'per_page' => $limit,
            'page_query_string' => true,
            'reuse_uery_string' => true,
            'num_links' => 5,
            'total_rows' => $total
        ));

        $this->include_js(array(
            'jquery.cookie.js',
            'sweetalert.min.js',
            'bootstrap-select/bootstrap-select.js',
            "b3-datetimepicker.min.js"
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            "b3-datetimepicker.min.css",
            'sweetalert.css'
        ));

        $this->render('controle_pendencias/default', array(
            'itens' => $partNumbersPerguntas,
            'responsible_type' => $responsible_type,
            'partNumbersFilter' => $partNumbersFilter,
            'usuariosFilterAtivos' => $usuariosFilterAtivos,
            'usuariosFilterInativos' => $usuariosFilterInativos,
            'responsaveis_ativos' => $responsaveis_ativos,
            'status' => $status,
            'usuarios' => $usuarios,
            'partnumbers' => $partnumbers,
            'bulk_selection'  => $bulk,
            'periodo_inicio'  => $periodo_inicio,
            'periodo_fim'  => $periodo_fim,
            'pesquisa' => $pesquisa,
            'owners' => $owners,
            'ownersFilter' => $ownersFilter,
            'hasOwner' => $hasOwner,
            'ownersToTransfer' => $ownersToTransfer,
            'hasPrioridade' => $hasPrioridade,
            'prioridades' => $prioridades,
            'prioridadesFilter' => $prioridadesFilter,
        ));
    }

    public function responder()
    {

        $this->load->model(['ctr_pendencias_pergunta_model', 'item_model']);
        $this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);

        $estabelecimento_itens =  array($this->input->get('estabelecimento'));
        $unico =  $this->input->get('unico');
        $partnumbers = $this->input->is_set("itens") ? $this->input->get("itens") : array($this->input->get('partnumber'));
        $responder_dados =  $this->input->get('responder') == '1' ? true : false;
        $clean_bulk =  $this->input->get('clean_bulk') == '1' ? true : false;

        $bulk = $this->ctr_pendencias_pergunta_model->getBulk();
        $estabelecimento = $estabelecimento_itens;
        $item = $partnumbers;

        if (!empty($bulk) && $unico != true && !$clean_bulk) {
            $partnumbers = $bulk['item'];
            $item  = [];
            $estab = [];
            foreach ($partnumbers as $pn) {
                $itens = explode("&", $pn);
                $item[]  = $itens[0];
                $estab[] = isset($itens[1]) ? $itens[1] : null;
            }
        } elseif (!empty($responder_dados) && $responder_dados && $clean_bulk) {
            $this->bulk_clean();
            $item = [];
            $estab = [];

            $pn =  $this->input->get('partnumber');
            $itens = explode("|", $pn);

            $item[]  = $itens[0];
            $estab[] = isset($itens[1]) ? $itens[1] : null;
        } elseif (!empty($responder_dados) && $responder_dados == '1') {
            $item = [];
            $estab = [];

            $pn =  $this->input->get('partnumber');
            $itens = explode("|", $pn);

            $item[]  = $itens[0];
            $estab[] = isset($itens[1]) ? $itens[1] : null;
        }

        if (!empty($item) && !empty($estab)) {
            $this->item_model->set_usuario_bloqueador_itens(sess_user_company(), $item, $estab);
        }

        $tooltips = [];
        foreach ($item as $i) {
            $item_x = $this->item_model->get_entry($i, sess_user_company());
            if (!empty($item_x->descricao_global)) {
                $descricao = $item_x->descricao_global;
            } else if (!empty($item_x->descricao)) {
                $descricao = $item_x->descricao;
            } else {
                $descricao = 'Sem Descrição';
            }
            $tooltips[$item_x->part_number] = $descricao;
        }

        $this->include_js(array(
            'sweetalert.min.js'
        ));

        $this->include_css(array(
            'sweetalert.css'
        ));

        if (!isset($estab) || empty($estab))
            $estab = $estabelecimento;

        $this->breadcrumbs->push('Responder pendências', '/controle_pendencias/responder');

        // Verificar se empresa tem DIANA habilitado
        $diana_enabled = $this->has_diana_permission(sess_user_company());

        $this->render('controle_pendencias/responder', array(
            'partnumbers' => $item,
            'estabelecimento' => $estab,
            'tooltips' => $tooltips,
            'diana_enabled' => $diana_enabled
        ));
    }

    /**
     * Verifica se a empresa tem permissão para usar DIANA
     *
     * @param int $id_empresa
     * @return bool
     */
    private function has_diana_permission($id_empresa)
    {
        if (!$id_empresa) {
            return false;
        }

        // Carregar empresa model
        $this->load->model('empresa_model');

        $empresa = $this->empresa_model->get_entry($id_empresa);

        if (!$empresa || !$empresa->funcoes_adicionais) {
            return false;
        }

        $funcoes = explode('|', $empresa->funcoes_adicionais);
        return in_array('responder_perguntas_diana', $funcoes);
    }

    private function clear_filter()
    {
        $this->load->model([
            'ctr_pendencias_pergunta_model'
        ]);

        $this->ctr_pendencias_pergunta_model->set_state_store_session(TRUE);

        $this->ctr_pendencias_pergunta_model->unset_state('filter.pendente');
        $this->ctr_pendencias_pergunta_model->unset_state('filter.partnumbers');
        $this->ctr_pendencias_pergunta_model->unset_state('filter.periodoIni');
        $this->ctr_pendencias_pergunta_model->unset_state('filter.periodoFim');
        $this->ctr_pendencias_pergunta_model->unset_state('filter.status');
        $this->ctr_pendencias_pergunta_model->unset_state('filter.prioridade');
        $this->ctr_pendencias_pergunta_model->unset_state('filter.partnumber_like');
    }

    public function perguntas()
    {
        $this->breadcrumbs->push('Perguntas', '/controle_pendencias/perguntas');

        $this->clear_filter();
        // Garantir que $status sempre seja um array válido para evitar warnings no in_array()
        $status_input = $this->input->get("status");
        $status = ($this->input->is_set("status") && is_array($status_input)) ? $status_input : array('0', '1');
        // $this->ctr_pendencias_pergunta_model->set_state('filter.id_usuario_pergunta', sess_user_id());
        $this->ctr_pendencias_pergunta_model->unset_state('filter.usuarioPergunta');
        $this->ctr_pendencias_pergunta_model->set_state('filter.status', $status);
        $this->ctr_pendencias_pergunta_model->set_state('filter.prioridade', $this->input->get('prioridade'));
        $this->ctr_pendencias_pergunta_model->set_state('filter.partnumber_like', $this->input->get('partnumber'));
        $this->ctr_pendencias_pergunta_model->set_state('filter.periodoFim', $this->input->get('periodoFim'));
        $this->ctr_pendencias_pergunta_model->set_state('filter.periodoIni', $this->input->get('periodoIni'));

        $per_page = $this->input->get('per_page');

        $limit = 20;
        $offset = $per_page;

        $perguntasRealizadas = $this->ctr_pendencias_pergunta_model->getPerguntasRealizadas($limit, $offset);
        $total = $this->ctr_pendencias_pergunta_model->getPerguntasRealizadas(NULL, NULL, true);


        $partnumber = $this->input->is_set("partnumber") ? $this->input->get("partnumber") : '';
        $this->load->library('pagination');
        $this->pagination->initialize(array(
            'base_url' => base_url('/controle_pendencias/perguntas'),
            'uri_segment' => 3,
            'per_page' => $limit,
            'page_query_string' => true,
            'reuse_uery_string' => true,
            'num_links' => 5,
            'total_rows' => $total
        ));

        $this->include_js(array(
            'jquery.cookie.js',
            'sweetalert.min.js',
            'bootstrap-select/bootstrap-select.js',
            "b3-datetimepicker.min.js"
        ));

        $this->include_css(array(
            'bootstrap-select/bootstrap-select.css',
            "b3-datetimepicker.min.css",
            'sweetalert.css'
        ));
        $this->render('controle_pendencias/perguntas', array(
            'itens' => $perguntasRealizadas,
            'status' => $status,
            'partnumber' => $partnumber
        ));
    }

    public function ajax_get_questions()
    {
        $this->load->model('ctr_pendencias_pergunta_model');

        $user_email = $this->usuario_model->get_email_user(sess_user_id());
        $data = array();
        $data['perguntas'] = $this->ctr_pendencias_pergunta_model->get_questions_to_answer($user_email, sess_user_company(), 2);

        $this->load->view('controle_pendencias/form_user_popover', $data);
    }

    public function novo()
    {

        $this->load->model('ctr_pendencias_pergunta_model');
        $this->load->model('ctr_pendencias_item_model');

        if ($pergunta = $this->input->post()) {

            if (empty($pergunta['desc_question'])) {
                echo json_encode(array('erro' => 'Informe a pergunta que deseja fazer'));
                return TRUE;
            }

            $data['texto_pergunta'] = $pergunta['desc_question'];
            $data['ncm'] = $pergunta['ncm_question'];

            if ($pergunta['answer_type'] == 'unique' || $pergunta['answer_type'] == 'multiple') {
                $data['opcoes'] = "";
                for ($i = 0; $i < $pergunta['total_options']; $i++) {
                    if ((string) $pergunta['option_question_' . ($i + 1)] != "") {
                        if ($i > 0) $data['opcoes'] .= "|";
                        $data['opcoes'] .= $pergunta['option_question_' . ($i + 1)];
                    }
                }

                if (empty($data['opcoes'])) {
                    echo json_encode(array('erro' => 'Informe as opções de resposta'));
                    return TRUE;
                }
            }

            $data['id_empresa'] = sess_user_company();
            $data['id_usuario_pergunta'] = sess_user_id();
            $data['tipo_resposta'] = $pergunta['answer_type'];
            $data['data_pergunta'] = date('Y-m-d H:i:s');

            $id_pergunta = $this->ctr_pendencias_pergunta_model->save($data);

            if (empty($pergunta['desc_itens_question'])) {
                echo json_encode(array('erro' => 'Informe os itens relacionados á pergunta'));
                return TRUE;
            }

            $itens = explode(" ", $pergunta['desc_itens_question']);

            $this->load->model('item_log_model');

            foreach ($itens as $cod_item) {
                $data_item = array('part_number' => $cod_item);

                $part_numbers[] = $cod_item;

                if (isset($pergunta['email_share'])) {
                    $data_item['usuario_especifico'] = $pergunta['email_share'];
                }

                $id_ctr_item = $this->ctr_pendencias_item_model->save_item_rel_pergunta($data_item, $id_pergunta);

                $this->load->model('item_model');
                $ctr_item = $this->ctr_pendencias_item_model->get_entry($id_ctr_item);

                try {

                    $item = $this->item_model->get_entry($ctr_item->part_number, sess_user_company());

                    if (isset($item)) {
                        $log_data['titulo'] = 'pendenciacadastrada';
                        $log_data['motivo'] = 'Pergunta: ' . $pergunta['desc_question'];
                        $log_data['part_number'] = $item->part_number;
                        $log_data['estabelecimento'] = $item->estabelecimento;
                        $log_data['id_usuario'] = sess_user_id();
                        $log_data['id_empresa'] = sess_user_company();
                        $log_data['criado_em'] = date('Y-m-d H:i:s');

                        $this->item_log_model->save($log_data);
                    }
                } catch (Exception $e) {
                    //ITEM NÃO CADASTRADO
                }
            }

            if (isset($pergunta['email_share'])) {
                $this->load->model('usuario_model');
                $usuario_id = $this->usuario_model->get_user_id_by_email($pergunta['email_share']);
                $usuario = $this->usuario_model->get_entry($usuario_id);
                $usuario_origem = $this->usuario_model->get_entry(sess_user_id());

                if (customer_has_role('sysadmin', $usuario_id)) {
                    $url = config_item('online_url') . 'controle_pendencias/listar_pendencias';
                } else {
                    $url = config_item('online_url') . 'controle_pendencias/listar';
                }

                $html_message = '
                    <h3>Cadastro de nova pendência</h3>

                    <br>

                    <p>Olá, ' . $usuario->nome . '!</p>
                    <p>Você possui uma nova pendência no portal, verifique abaixo as informações: </p>

                    <div class="panel panel-default">
                        <div class="panel-body" style="padding: 20px;border-left: 3px solid #ccc;background: #FBFBFB;">
                            <p><strong>Pendência</strong>: ' . $pergunta['desc_question'] . '</p>
                            <p><strong>NCM</strong>: ' . $pergunta['ncm_question'] . '</p>
                            <p><strong>Part number(s)</strong>: ' . implode(", ", $part_numbers) . '</p>
                            <p><strong>Usuário de origem: </strong> ' . $usuario_origem->nome . '</p>
                        </div>
                    </div>

                    <p style="text-align: right;"><a href="' . $url . '">Clique aqui para acompanhar no portal</a></p>
                ';

                $temp_data = array(
                    'base_url' => config_item('online_url'),
                    'html_message' => $html_message
                );

                $body = $this->load->view('templates/basic_template', $temp_data, TRUE);

                $this->load->library('email');

                $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
                $this->email->to($pergunta['email_share']);
                $this->email->subject('[Gestão Tarifária] - Nova pendência cadastrada');
                $this->email->message($body);

                $this->email->send();
            }
        }

        echo json_encode(array('sucess' => TRUE));
        return TRUE;
    }

    // public function responder()
    // {
    //     if ($resposta = $this->input->post())
    //     {

    //         $this->load->model('ctr_pendencias_resposta_model');
    //         $this->load->model('ctr_pendencias_item_model');
    //         $this->load->model('item_model');

    //         $ctr_item = $this->ctr_pendencias_item_model->get_entry($resposta['id_ctr_item']);

    //         try{
    //             $item = $this->item_model->get_entry($ctr_item->part_number, sess_user_company());
    //         }catch (Exception $e)
    //         {
    //             //Part number não existe na tabela item
    //         }

    //         if ($resposta['answer_type'] == 'unique' || $resposta['answer_type'] == 'multiple')
    //         {
    //             if (empty($resposta['option']))
    //             {
    //                 echo json_encode(array('erro' => 'Selecione uma opção'));
    //                 return TRUE;
    //             }
    //         }else if ($resposta['answer_type'] == 'text')
    //         {
    //             if (empty($resposta['option']))
    //             {
    //                 echo json_encode(array('erro' => 'Escreva a resposta que deseja enviar'));
    //                 return TRUE;
    //             }
    //         }

    //         if (isset($resposta['option']))
    //         {
    //             if (is_array($resposta['option']) && count($resposta['option']) > 0)
    //             {
    //                 $data['opcao_resposta'] = implode("|", $resposta['option']);
    //             }else
    //             {
    //                 $data['opcao_resposta'] = $resposta['option'];
    //             }
    //         }

    //         $data['comentario'] = $resposta['comment'];
    //         $data['id_usuario_resposta'] = sess_user_id();
    //         $data['data_resposta'] = date('Y-m-d H:i:s');

    //         $this->load->library('upload');

    //         if (!empty($_FILES['arq_anexo']['name']))
    //         {

    //             $name = substr($_FILES['arq_anexo']['name'], 0, strpos($_FILES['arq_anexo']['name'], "."));
    //             $ext = '.' . strtolower(end(explode('.', $_FILES['arq_anexo']['name'])));
    //             $file_name = strtolower(url_title(iconv('UTF-8', 'ASCII//TRANSLIT', $name))) . $ext;

    //             $config = array();

    //             $config['file_name'] = $file_name;
    //             $config['max_size'] = 5000;

    //             $ds = DIRECTORY_SEPARATOR;
    //             $path = FCPATH . 'assets'.$ds.'anexos';
    //             if (!file_exists($path))
    //             {
    //                 mkdir($path, 0777);
    //             }

    //             $path .= $ds.sess_user_company().$ds;
    //             if (!file_exists($path))
    //             {
    //                 mkdir($path, 0777);
    //             }

    //             $config['upload_path'] = $path;
    //             $config['allowed_types'] = 'doc|docx|pdf|xls|xlsx|tif|tiff';

    //             $this->upload->initialize($config);

    //             if ($this->upload->do_upload('arq_anexo'))
    //             {
    //                 $data['arquivo_anexo'] = $file_name;
    //             }else
    //             {
    //                 echo json_encode(array('erro' => strip_tags($this->upload->display_errors())));
    //                 return TRUE;
    //             }
    //         }

    //         if (!empty($_FILES['anexo']['name']))
    //         {

    //             $name = substr($_FILES['anexo']['name'], 0, strpos($_FILES['anexo']['name'], "."));
    //             $ext = '.' . strtolower(end(explode('.', $_FILES['anexo']['name'])));
    //             $file_name = strtolower(url_title(iconv('UTF-8', 'ASCII//TRANSLIT', $name))) . $ext;

    //             $config = array();

    //             $config['file_name'] = $file_name;
    //             $config['max_size'] = 5000;

    //             $ds = DIRECTORY_SEPARATOR;
    //             $path = FCPATH . 'assets'.$ds.'fotos'.$ds.sess_user_company().$ds;
    //             if (!file_exists($path))
    //             {
    //                 mkdir($path);
    //             }
    //             $config['upload_path'] = $path;
    //             $config['allowed_types'] = 'png|jpg|tif|tiff';

    //             $this->upload->initialize($config);

    //             if ($this->upload->do_upload('anexo'))
    //             {
    //                 $data['anexo'] = $file_name;

    //                 $this->load->model('foto_model');

    //                 if (isset($item))
    //                 {

    //                     $foto['arquivo'] = $this->upload->data()['file_name'];
    //                     $foto['id_empresa'] = sess_user_company();
    //                     $foto['part_number'] = $item->part_number;
    //                     $foto['estabelecimento'] = $item->estabelecimento;
    //                     $foto['ordem'] = $this->foto_model->get_next_ordem($item->part_number, $item->id_empresa, $item->estabelecimento);

    //                     $this->foto_model->save($foto);

    //                 }
    //             }else
    //             {
    //                 echo json_encode(array('erro' => strip_tags($this->upload->display_errors())));
    //                 return TRUE;
    //             }
    //         }

    //         if (isset($resposta['repeat_answer']))
    //         {
    //             $this->load->model('ctr_pendencias_pergunta_model');
    //             $rs_itens = $this->ctr_pendencias_pergunta_model->get_itens_from_question($resposta['id_ctr_pergunta']);

    //             $id_ctr_resposta = $this->ctr_pendencias_resposta_model->save($data);

    //             foreach ($rs_itens as $row_item)
    //             {
    //                 $data['id_ctr_item'] = $row_item->id_ctr_item;
    //                 $this->ctr_pendencias_resposta_model->save_resposta_rel_item($data, $id_ctr_resposta);
    //             }
    //         }else
    //         {
    //             $data['id_ctr_item'] = $resposta['id_ctr_item'];
    //             $this->ctr_pendencias_resposta_model->save_resposta_rel_item($data);
    //         }

    //     }

    //     if (isset($item))
    //     {

    //         $this->load->model('item_log_model');

    //         $log_data['titulo'] = 'pendenciarespondida';
    //         $log_data['motivo'] = 'Resposta: ' . (!empty($data['opcao_resposta'])?$data['opcao_resposta']:$data['anexo']);
    //         $log_data['part_number'] = $item->part_number;
    //         $log_data['estabelecimento'] = $item->estabelecimento;
    //         $log_data['id_usuario'] = sess_user_id();
    //         $log_data['id_empresa'] = sess_user_company();
    //         $log_data['criado_em'] = date('Y-m-d H:i:s');

    //         $this->item_log_model->save($log_data);
    //     }

    //     $this->load->model('usuario_model');

    //     $user_email = $this->usuario_model->get_email_user(sess_user_id());

    //     $total = $this->ctr_pendencias_pergunta_model->count_questions_to_answer($user_email, sess_user_company());
    //     echo json_encode(array('total_perguntas' => $total));

    // }

    public function listar()
    {

        $data['lista_perguntas'] = TRUE;
        $this->load->model('ctr_pendencias_pergunta_model');
        $this->load->library('pagination');

        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Controle de pendências', '/controle_pendencias/listar/');

        if ($get = $this->input->get()) {
            if (!empty($get['data_ini'])) {
                $this->ctr_pendencias_pergunta_model->set_state('filter.data_ini', $get['data_ini']);
                $data['data_ini'] = $get['data_ini'];
            }
            if (!empty($get['data_fim'])) {
                $this->ctr_pendencias_pergunta_model->set_state('filter.data_fim', $get['data_fim']);
                $data['data_fim'] = $get['data_fim'];
            }
            if (!empty($get['data_resposta_ini'])) {
                $this->ctr_pendencias_pergunta_model->set_state('filter.data_resposta_ini', $get['data_resposta_ini']);
                $data['data_resposta_ini'] = $get['data_resposta_ini'];
            }
            if (!empty($get['data_resposta_fim'])) {
                $this->ctr_pendencias_pergunta_model->set_state('filter.data_resposta_fim', $get['data_resposta_fim']);
                $data['data_resposta_fim'] = $get['data_resposta_fim'];
            }
            if (!empty($get['status'])) {
                $this->ctr_pendencias_pergunta_model->set_state('filter.status', $get['status']);
                $data['status'] = $get['status'];
            }
            if (!empty($get['part_number'])) {
                $this->ctr_pendencias_pergunta_model->set_state('filter.part_number', $get['part_number']);
                $data['part_number'] = $get['part_number'];
            }
        }

        if (has_role('sysadmin') || has_role('becomex_pmo')) {

            $this->ctr_pendencias_pergunta_model->set_state('filter.id_empresa', sess_user_company());

            $per_page = $this->input->get('per_page');

            $this->load->library('pagination');

            $limit = 20;
            $offset = $per_page;

            $total_rows = $this->ctr_pendencias_pergunta_model->count_questions_from_owner(sess_user_id());

            $config['base_url'] = 'listar';
            $config['uri_segment'] = 3;
            $config['total_rows'] = $total_rows;
            $config['per_page'] = $limit;
            $config['page_query_string'] = TRUE;
            $config['reuse_query_string'] = TRUE;
            $config['num_links'] = 5;

            $this->pagination->initialize($config);

            $data['perguntas'] = $this->ctr_pendencias_pergunta_model->get_questions_from_owner(sess_user_id(), $limit, $offset);
            $data['total_rows'] = $total_rows;

            $this->render('controle_pendencias/listar_admin', $data);
        } else {
            $user_email = $this->usuario_model->get_email_user(sess_user_id());
            $data['perguntas'] = $this->ctr_pendencias_pergunta_model->get_questions_to_answer($user_email, sess_user_company());
            $this->render('controle_pendencias/listar', $data);
        }
    }

    public function listar_pendencias()
    {
        $this->breadcrumbs->push('Home', '/');
        $this->breadcrumbs->push('Controle de pendências', '/controle_pendencias/listar/');

        $user_email = $this->usuario_model->get_email_user(sess_user_id());
        $data['perguntas'] = $this->ctr_pendencias_pergunta_model->get_questions_to_answer($user_email, sess_user_company());
        $this->render('controle_pendencias/listar', $data);
    }

    public function sugerir()
    {
        $post = $this->input->post();
        $this->load->model('ctr_pendencias_item_model');
        $this->load->model('ctr_pendencias_pergunta_model');
        $this->load->model('usuario_model');

        if ($id_usario_sugerido = $this->usuario_model->get_user_id_by_email($post['email_share'])) {
            $ctr_item = $this->ctr_pendencias_item_model->get_entry($post['id_ctr_item']);

            if (!empty($ctr_item->usuario_especifico)) {
                $id_old_usuario = $this->usuario_model->get_user_id_by_email($ctr_item->usuario_especifico);
                $old_usuario = $this->usuario_model->get_entry($id_old_usuario);
            }

            $new_usuario = $this->usuario_model->get_entry($id_usario_sugerido);

            $this->ctr_pendencias_item_model->save(
                array(
                    'usuario_especifico' => $post['email_share'],
                    'observacoes' => (!empty($post['comment']) ? $post['comment'] : NULL)
                ),
                array('id_ctr_item' => $post['id_ctr_item'])
            );

            $user_email = $this->usuario_model->get_email_user(sess_user_id());
            $total = $this->ctr_pendencias_pergunta_model->count_questions_to_answer($user_email, sess_user_company());

            $this->save_log_transferido($post['email_share'], $post['id_ctr_item'], (!empty($post['comment']) ? $post['comment'] : NULL));

            $this->notifica_transferencia($old_usuario, $new_usuario);

            echo json_encode(array('total_perguntas' => $total));
        } else {
            echo json_encode(array('erro' => 'Usuário não encontrado'));
        }
    }

    public function detalhe()
    {
        if ($get = $this->input->get()) {
            $this->load->model('ctr_pendencias_pergunta_model');
            $this->load->model('ctr_pendencias_item_model');

            if (!empty($get['usuario_especifico'])) {
                $id_new_usuario = $this->usuario_model->get_user_id_by_email($get['usuario_especifico']);
                $new_usuario = $this->usuario_model->get_entry($id_new_usuario);

                $ctr_item = $this->ctr_pendencias_item_model->get_entry($get['item']);

                $usuario_esp = array('usuario_especifico' => $get['usuario_especifico']);
                $this->ctr_pendencias_item_model->save($usuario_esp, array('id_ctr_item' => $get['item']));

                $this->save_log_transferido($get['usuario_especifico'], $get['item']);

                if (!empty($ctr_item->usuario_especifico)) {
                    $id_old_usuario = $this->usuario_model->get_user_id_by_email($ctr_item->usuario_especifico);
                    $old_usuario = $this->usuario_model->get_entry($id_old_usuario);
                }

                $this->notifica_transferencia($old_usuario, $new_usuario);

                redirect('controle_pendencias/detalhe?pergunta=' . $get['pergunta'] . '&item=' . $get['item']);
            }

            $data['pergunta'] = $this->ctr_pendencias_pergunta_model->get_question_detail($get['pergunta'], $get['item'], '');

            $this->breadcrumbs->push('Home', '/');
            $this->breadcrumbs->push('Controle de pendências', '/controle_pendencias/listar/');
            $this->breadcrumbs->push('Detalhe da pendência', '/controle_pendencias/detalhe?pergunta=' . $get['pergunta'] . '&item=' . $get['item']);

            $this->render('controle_pendencias/detalhe', $data);
        }
    }

    public function save_log_transferido($email, $id_ctr_item, $observacoes = NULL)
    {

        $this->load->model('ctr_pendencias_item_model');
        $this->load->model('item_model');

        $ctr_item = $this->ctr_pendencias_item_model->get_entry($id_ctr_item);

        try {
            $item = $this->item_model->get_entry($ctr_item->part_number, sess_user_company());
        } catch (Exception $e) {
            //Part number não existe na tabela item
        }

        if (isset($item)) {

            $this->load->model('item_log_model');
            $this->load->model('cad_item_model');

            $log_data['titulo'] = 'pendenciatransferida';
            $log_data['motivo'] = 'Novo usuário: ' . $email . (!empty($observacoes) ? ' / Observação: ' . $observacoes : "");
            $log_data['part_number'] = $item->part_number;
            $log_data['estabelecimento'] = $item->estabelecimento;
            $log_data['id_usuario'] = sess_user_id();
            $log_data['id_empresa'] = sess_user_company();
            $log_data['criado_em'] = date('Y-m-d H:i:s');

            $this->item_log_model->save($log_data);
        }
    }

    public function change_read_flag($id_ctr_resposta, $lida = NULL, $id_ctr_pergunta, $id_ctr_item)
    {
        if (!empty($id_ctr_resposta)) {
            $this->load->model('ctr_pendencias_resposta_model');

            if ($lida == 1) {
                $lida = 0;
            } else {
                $lida = 1;
            }

            $this->ctr_pendencias_resposta_model->save(array('lida' => $lida), array('id_ctr_resposta' => $id_ctr_resposta));

            $this->message_next_render('<strong>OK! </strong> A resposta foi marcada com sucesso.', 'success');
            redirect('controle_pendencias/detalhe?pergunta=' . $id_ctr_pergunta . '&item=' . $id_ctr_item);
            return TRUE;
        }
    }

    public function notifica_transferencia($old_usuario, $new_usuario)
    {
        // Envio de notificação por e-mail

        $usuario_origem = $this->usuario_model->get_entry(sess_user_id());

        if (customer_has_role('sysadmin', $new_usuario->id_usuario)) {
            $url = config_item('online_url') . 'controle_pendencias/listar_pendencias';
        } else {
            $url = config_item('online_url') . 'controle_pendencias/listar';
        }

        $html_message = '
            <h3>Cadastro de nova pendência</h3>

            <br>

            <p>Olá, ' . $new_usuario->nome . '!</p>
            <p>O usuário ' . $usuario_origem->nome . ' acaba de definí-lo como responsável por uma pendência.';

        if (isset($old_usuario) && $old_usuario->id_usuario != $new_usuario->id_usuario) {
            $html_message .= '<div class="panel panel-default">
                    <div class="panel-body" style="padding: 20px;border-left: 3px solid #ccc;background: #FBFBFB;">
                        <p><strong>Responsável anterior</strong>: ' . $old_usuario->nome . '</p>
                    </div>
                </div>';
        }

        $html_message .= '<p style="text-align: right;"><a href="' . $url . '">Clique aqui para acompanhar no portal</a></p>';

        $temp_data = array(
            'base_url' => config_item('online_url'),
            'html_message' => $html_message
        );

        $body = $this->load->view('templates/basic_template', $temp_data, TRUE);

        $this->load->library('email');

        $this->email->from(config_item('mail_from_addr'), config_item('mail_from_name'));
        $this->email->to($new_usuario->email);
        $this->email->subject('[Gestão Tarifária] - Nova pendência cadastrada');
        $this->email->message($body);

        $this->email->send();
    }

    public function transferirPerguntasPendentes()
    {
        $partnumber = $this->input->post("partnumber");
        $responsaveis = $this->input->post("idResponsavel");
        $estabelecimento = $this->input->post("estabelecimento");
        $ownerResponsavel = $this->input->post("ownerResponsavel");
        $tipoResponsavel = $this->input->post("tipoResponsavel");

        try {
            if (empty($partnumber)) {
                $this->load->model('ctr_pendencias_pergunta_model');
                $this->ctr_pendencias_pergunta_model->set_state_store_session(true);
                $bulk = $this->ctr_pendencias_pergunta_model->getBulk();

                if (!empty($bulk)) {
                    $partnumbers = $bulk['item'];
                    foreach ($partnumbers as $pn) {
                        $itens = explode("&", $pn);
                        $item = $itens[0];

                        if (count($itens) > 2) {
                            // Se o array itens tiver mais de 2 itens, concatena a partir do índice 1
                            $estabelecimento = implode("&", array_slice($itens, 1));
                        } else {
                            $estabelecimento = $itens[1];
                        }

                        $this->ctr_pendencias_pergunta_model
                            ->transferirResponsavel(
                                $responsaveis,
                                $item,
                                $estabelecimento,
                                $ownerResponsavel,
                                $tipoResponsavel
                            );
                    }
                }
            } else {
                $this->ctr_pendencias_pergunta_model
                    ->transferirResponsavel(
                        $responsaveis,
                        $partnumber,
                        $estabelecimento,
                        $ownerResponsavel,
                        $tipoResponsavel
                    );
            }

            return response_json(array(
                "success" => true,
                "message" => "Sucesso! As perguntas foram transferidas."
            ));
        } catch (Exception $e) {
            return response_json(array(
                "success" => false,
                "message" => "Erro! Algo inesperado aconteceu, contate um administrador."
            ));
        }
    }
}
