<div class="row">
    <div class="col-sm-12">
        <div class="historico-table-wrapper table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Estabelecimento</th>
                        <th>Pergunta</th>
                        <th>Data pergunta</th>
                        <th>Resposta</th>
                        <th>Data resposta</th>
                        <th>Responsável</th>
                        <th>Respondido por</th>
                        <th>Arquivos</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($perguntas as $pergunta) : ?>
                        <tr>
                            <td class="col-dado"><?php echo !empty($pergunta->estabelecimento) ? $pergunta->estabelecimento : 'N/A' ?></td>
                            <td class="col-dado col-pergunta"><?php echo $pergunta->pergunta ?></td>
                            <td class="col-dado"><?php echo date('d/m/Y H:i:s', strtotime($pergunta->criado_em)); ?></td>
                            <td class="col-dado">
                                <?php if ($pergunta->pendente == 1) : ?>
                                    <?php echo '-'; ?>
                                <?php else : ?>
                                    <?php echo !empty($pergunta->resposta) ? $pergunta->resposta : 'Sem resposta' ?>
                                <?php endif; ?>
                            </td>
                            <td class="col-dado"><?php echo !empty($pergunta->resposta) ? date('d/m/Y H:i:s', strtotime($pergunta->respondida_em)) : '-' ?></td>
                            <td class="col-dado"><?php echo $pergunta->nome ? $pergunta->nome : $pergunta->cod_owner . ' - ' . $pergunta->owner ?></td>
                            <td class="col-dado"><?php echo $pergunta->usu_resposta ?></td>
                            <td class="col-arquivos">
                                <?php if (!empty($pergunta->arquivo)) : ?>
                                    <?php $dropdownId = 'dropdownMenuArquivos-' . uniqid(); ?>
                                    <div class="dropdown arquivos-dropdown">
                                        <button class="btn btn-default dropdown-toggle" type="button" id="<?php echo $dropdownId; ?>" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="glyphicon glyphicon-download"></i>
                                            <?php $totalArquivos = is_array($pergunta->arquivo) || $pergunta->arquivo instanceof Countable ? count($pergunta->arquivo) : count((array) $pergunta->arquivo); ?>
                                            <?php if ($totalArquivos > 1) : ?>
                                                <span class="badge badge-arquivos"><?php echo $totalArquivos; ?></span>
                                            <?php endif; ?>
                                        </button>
                                        <div class="dropdown-menu dropdown-menu-right" aria-labelledby="<?php echo $dropdownId; ?>">
                                            <?php foreach ($pergunta->arquivo as $arquivo) : ?>
                                                <a class="dropdown-item btn-download" title="<?php echo $arquivo->nome ?>" href="<?php echo base_url() ?>assets/respostas/<?php echo $arquivo->nome ?>" download>
                                                    <?php echo $arquivo->nome ?>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php else : ?>
                                    <span class="sem-arquivos">-</span>
                                <?php endif; ?>
                            </td>

                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<style scoped>
    .form-group.files .form-control {
        padding: 5px 4px;
    }

    .historico-table-wrapper {
        width: 100%;
        margin: 0 -4px;
        padding: 0 4px;
    }

    .historico-table-wrapper table {
        width: 100%;
        margin-bottom: 0;
        table-layout: fixed;
    }

    .historico-table-wrapper thead th {
        white-space: nowrap;
        font-weight: 600;
    }

    .col-dado {
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        vertical-align: middle;
    }

    .col-pergunta {
        max-width: 360px;
        white-space: normal;
        overflow: visible;
        text-overflow: initial;
        word-break: break-word;
        line-height: 1.35;
    }

    .col-arquivos {
        min-width: 120px;
        overflow: visible;
        text-align: center;
        white-space: nowrap;
    }

    .col-arquivos .sem-arquivos {
        color: #999;
        font-style: italic;
    }

    .arquivos-dropdown {
        position: relative;
        display: inline-block;
    }

    .arquivos-dropdown .btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 42px;
        padding: 6px 10px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    }

    .badge-arquivos {
        position: absolute;
        top: -5px;
        right: -5px;
        background-color: #5cb85c;
        color: #fff;
        border-radius: 50%;
        font-size: 10px;
        padding: 3px 6px;
    }

    .arquivos-dropdown .dropdown-menu {
        min-width: 220px;
        max-height: 240px;
        overflow-y: auto;
        padding: 8px 0;
        margin-top: 8px;
        border-radius: 6px;
        border: none;
        box-shadow: 0 12px 32px rgba(15, 23, 42, 0.18);
        right: 0;
        left: auto;
        z-index: 2050;
    }

    .btn-download {
        display: block;
        padding: 6px 16px;
        color: #333;
        border-bottom: 1px solid rgba(203, 213, 225, 0.8);
        transition: background-color 0.2s ease, color 0.2s ease;
    }

    .btn-download:hover,
    .btn-download:focus {
        background-color: #f0f4ff;
        color: #1c3faa;
        text-decoration: none;
    }

    .btn-download:last-child {
        border-bottom: none;
    }

    .form-control .input-file {
        padding: 5px 5px !important;
    }
</style>