import Vue from './vue.js';
import BtnDianaDescricaoCompletaResumida from "./components/DianaDescricaoProposta/BtnDianaDescricaoCompletaResumida";
import BtnDianaDescricaoCompleta from "./components/DianaDescricaoProposta/BtnDianaDescricaoCompleta";
import BtnDianaDescricaoResumida from "./components/DianaDescricaoProposta/BtnDianaDescricaoResumida";

Vue.component("v-btn-diana-descricao-completa-resumida", BtnDianaDescricaoCompletaResumida);
Vue.component("v-btn-diana-descricao-completa", BtnDianaDescricaoCompleta);
Vue.component("v-btn-diana-descricao-resumida", BtnDianaDescricaoResumida);


new Vue({}).$mount("#btn-diana-descricao-proposta-app");
